import { Buttons, IconOption } from '../models/image-to-code.model';

export const uiDesignConstants = {
  // Animated texts specific to UI Design generation
  animatedTexts: [
    'a modern landing page for..',
    'a mobile app interface for..',
    'a dashboard design with..',
    'a user profile page for..',
    'a product showcase page for..',
    'a contact form design for..',
    'a navigation menu for..',
    'a card-based layout for..',
    'a responsive design for..',
    'a minimalist interface for..',
  ],

  // Animated texts to show when an image is uploaded for UI Design
  imageUploadAnimatedTexts: [
    'a UI design based on this image..',
    'an interface that matches this style..',
    'a design system from this reference..',
    'a layout inspired by this mockup..',
    'a responsive design from this wireframe..',
    'a modern interface based on this concept..',
    'a user-friendly design from this example..',
    'a clean layout matching this style..',
    'a professional interface from this reference..',
    'a pixel-perfect design based on this image..',
  ],

  // Hero section content for UI Design
  heroSection: {
    title: 'Design Create Launch!',
    description: '<PERSON><PERSON><PERSON><PERSON> transforms your ideas into stunning UI designs with AI precision.',
    subHeading: 'What UI design would you like to create today?',
  },
};

// Application target options (mobile/web) for UI Design
// Note: Mobile is the default option (first in array)
export const applicationTargetOptions: IconOption[] = [
  {
    name: 'Mobile',
    icon: '/assets/icons/awe_mobile.svg', // Beautiful gradient mobile phone icon
    value: 'mobile',
    isLocalSvg: true,
  },
  {
    name: 'Web',
    icon: '/assets/icons/awe_web.svg', // Beautiful gradient desktop monitor icon
    value: 'web',
    isLocalSvg: true,
  },
];

// Suggestion buttons specific to UI Design
export const uiDesignButtonLabels: Buttons[] = [
  {
    label: '✨Design a modern e-commerce product page with clean aesthetics',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Create a dashboard interface for analytics with data visualization',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Design a mobile-first landing page for a SaaS product',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
];

// Design style options for UI Design (replacing framework selection)
export const designStyleOptions: IconOption[] = [
  {
    name: 'Modern',
    icon: '/assets/icons/awe_tailwind.svg', // Using existing icon as placeholder
    value: 'modern',
    isLocalSvg: true,
  },
  {
    name: 'Minimalist',
    icon: '/assets/icons/awe_material.svg', // Using existing icon as placeholder
    value: 'minimalist',
    isLocalSvg: true,
  },
  {
    name: 'Corporate',
    icon: '/assets/icons/awe_bootstrap.svg', // Using existing icon as placeholder
    value: 'corporate',
    isLocalSvg: true,
  },
];

// Color scheme options for UI Design
export const colorSchemeOptions: IconOption[] = [
  {
    name: 'Light Theme',
    icon: '/assets/icons/awe_react.svg', // Using existing icon as placeholder
    value: 'light',
    isLocalSvg: true,
  },
  {
    name: 'Dark Theme',
    icon: '/assets/icons/awe_vue.svg', // Using existing icon as placeholder
    value: 'dark',
    isLocalSvg: true,
  },
  {
    name: 'Auto Theme',
    icon: '/assets/icons/awe_angular.svg', // Using existing icon as placeholder
    value: 'auto',
    isLocalSvg: true,
  },
];

// Export all constants for easy import
export const UI_DESIGN_CONSTANTS = {
  ...uiDesignConstants,
  applicationTargetOptions,
  uiDesignButtonLabels,
  designStyleOptions,
  colorSchemeOptions,
};

export default UI_DESIGN_CONSTANTS;

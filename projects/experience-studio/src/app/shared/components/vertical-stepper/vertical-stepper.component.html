<div class="vertical-stepper" [ngClass]="theme">
  <div class="d-flex flex-column gap-4">
    <div class="stepper-item d-flex position-relative transition-all"
         *ngFor="let step of steps; let i = index; let isLast = last"
         [ngClass]="getStepClasses(i)"
         [class.hidden]="!shouldShowStep(i)">

      <!-- Connector Line -->
      <div class="step-line-container position-absolute"
           *ngIf="!isLast"
           [class.hidden-line]="isStepCollapsed(i)">
        <div class="step-line"
             [class.completed]="isStepCompleted(i)"
             [class.animating]="isLineAnimating(i)"></div>
      </div>

      <!-- Circle with Icon -->
      <div class="step-circle d-flex align-items-center justify-content-center flex-shrink-0 position-relative"
           [ngClass]="getCircleClasses(step, i)"
           (click)="toggleStepCollapse(i)">

        <!-- Success Icon -->
        <svg class="step-icon" *ngIf="showSuccessIcon(step, i)" width="24" height="24" viewBox="0 0 24 24">
          <circle cx="12" cy="12" r="12" fill="url(#successGradient)"/>
          <path d="M16.7 6.4L19 8.3L15.2 12.8L11.5 17.3L10.4 18.6L9.3 17.4L4.8 12.9L6.9 10.8L10.2 14.1L16.7 6.4Z" fill="white"/>
          <defs>
            <linearGradient id="successGradient" x1="0" y1="12" x2="24" y2="12">
              <stop stop-color="#6566CD"/><stop offset="1" stop-color="#E30A6D"/>
            </linearGradient>
          </defs>
        </svg>

        <!-- Error Icon -->
        <svg class="step-icon" *ngIf="showErrorIcon(step, i)" width="24" height="24" viewBox="0 0 24 24">
          <circle cx="12" cy="12" r="12" fill="url(#errorGradient)"/>
          <path d="M16.2 8.9L15.1 7.8L12 10.9L8.9 7.8L7.8 8.9L10.9 12L7.8 15.1L8.9 16.2L12 13.1L15.1 16.2L16.2 15.1L13.1 12L16.2 8.9Z" fill="white"/>
          <defs>
            <linearGradient id="errorGradient" x1="0" y1="12" x2="24" y2="12">
              <stop stop-color="#6566CD"/><stop offset="1" stop-color="#E30A6D"/>
            </linearGradient>
          </defs>
        </svg>

        <!-- Loading Spinner -->
        <div class="modern-loading-spinner position-absolute" *ngIf="showLoadingSpinner(step, i)">
          <div class="spinner-ring"></div>
          <div class="spinner-core"></div>
        </div>

        <!-- Step Number -->
        <span class="step-number text-semibold" *ngIf="showStepNumber(step, i)">{{ i + 1 }}</span>
      </div>

      <!-- Content -->
      <div class="step-content flex-grow-1 pt-1">
        <!-- Next Step Preview -->
        <div class="step-next opacity-60" *ngIf="getStepStatus(i) === 'next'">
          <div class="next-title h3 text-semibold mb-2">{{ formatTitle(step.visibleTitle || step.title) }}</div>
        </div>

        <!-- Active/Completed Step Content -->
        <div *ngIf="getStepStatus(i) !== 'next'" class="step-content-inner">
          <h3 class="step-title d-flex justify-content-between align-items-center mb-2 cursor-pointer transition-all"
              [class.typing]="step.isTitleTyping"
              (click)="toggleStepCollapse(i)">
            <span class="step-title-text text-semibold">{{ formatTitle(step.visibleTitle || step.title) }}</span>

            <!-- Timer -->
            <div class="step-timer"
                 [class.completed]="step.completionTime !== undefined && !step.timerActive"
                 *ngIf="showTimer(step)">
              {{ formatElapsedTime(step.completionTime || step.elapsedTime || 0) }}
            </div>

            <!-- Retry Button -->
            <button *ngIf="shouldShowRetryButton(step, i)"
                    class="step-retry-button d-flex align-items-center justify-content-center"
                    (click)="onRetryClick(i, $event)"
                    [title]="'Retry attempt ' + (step.retryCount || 0) + '/3'">
              <svg width="18" height="18" viewBox="0 0 24 24">
                <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4.01 7.58 4.01 12C4.01 16.42 7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12C6 8.69 8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z" fill="currentColor"/>
              </svg>
            </button>
          </h3>

          <!-- Description -->
          <div class="step-description transition-all"
               [class.collapsed]="isStepCollapsed(i)"
               [class.typing]="step.isTyping"
               [class.shimmer]="step.isRetrying">
            <markdown [data]="getSanitizedDescription(step.visibleDescription || step.description)"></markdown>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Restart Button -->
  <div class="d-flex justify-content-center mt-5" *ngIf="restartable">
    <button class="restart-button" (click)="restartStepper()">Restart Process</button>
  </div>
</div>

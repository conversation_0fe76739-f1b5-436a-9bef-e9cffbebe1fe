<div class="vertical-stepper" [ngClass]="theme">
  <div class="d-flex flex-column gap-4">
    <div class="stepper-item d-flex position-relative transition-all"
         *ngFor="let step of steps; let i = index; let isLast = last"
         [ngClass]="getStepClasses(i)"
         [class.hidden]="!shouldShowStep(i)">

      <!-- Connector Line -->
      <div class="step-line-container position-absolute" *ngIf="!isLast" [class.hidden-line]="isStepCollapsed(i)">
        <div class="step-line" [class.completed]="isStepCompleted(i)" [class.animating]="isLineAnimating(i)"></div>
      </div>

      <!-- Circle with Icon -->
      <div class="step-circle d-flex align-items-center justify-content-center flex-shrink-0 position-relative"
           [ngClass]="getCircleClasses(step, i)" (click)="toggleStepCollapse(i)">

        <!-- Dynamic Icon -->
        <ng-container [ngSwitch]="getStepIconType(step, i)">
          <img *ngSwitchCase="'success'"
               class="step-icon"
               src="assets/icons/vertical-stepper/success-icon.svg"
               alt="Success"
               width="24"
               height="24">
          <img *ngSwitchCase="'error'"
               class="step-icon"
               src="assets/icons/vertical-stepper/error-icon.svg"
               alt="Error"
               width="24"
               height="24">
          <div *ngSwitchCase="'loading'" class="modern-loading-spinner position-absolute">
            <div class="spinner-ring"></div><div class="spinner-core"></div>
          </div>
          <span *ngSwitchDefault class="step-number text-semibold">{{ i + 1 }}</span>
        </ng-container>
      </div>

      <!-- Content -->
      <div class="step-content flex-grow-1 pt-1">
        <ng-container [ngSwitch]="getStepStatus(i)">
          <!-- Next Step Preview -->
          <div *ngSwitchCase="'next'" class="step-next opacity-60">
            <div class="next-title h3 text-semibold mb-2">{{ getStepTitle(step) }}</div>
          </div>

          <!-- Active/Completed Step Content -->
          <div *ngSwitchDefault class="step-content-inner">
            <h3 class="step-title d-flex justify-content-between align-items-center mb-2 cursor-pointer transition-all"
                [class.typing]="step.isTitleTyping" (click)="toggleStepCollapse(i)">
              <span class="step-title-text text-semibold">{{ getStepTitle(step) }}</span>

              <!-- Timer & Retry Button -->
              <div class="d-flex align-items-center gap-2">
                <div class="step-timer" [class.completed]="isTimerCompleted(step)" *ngIf="showTimer(step)">
                  {{ getFormattedTime(step) }}
                </div>
                <button *ngIf="shouldShowRetryButton(step, i)" class="step-retry-button d-flex align-items-center justify-content-center"
                        (click)="onRetryClick(i, $event)" [title]="getRetryTitle(step)">
                  <img src="assets/icons/vertical-stepper/retry-icon.svg"
                       alt="Retry"
                       width="18"
                       height="18">
                </button>
              </div>
            </h3>

            <!-- Description -->
            <div class="step-description transition-all" [ngClass]="getDescriptionClasses(step, i)">
              <markdown [data]="getStepDescription(step)"></markdown>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
  </div>

  <!-- Restart Button -->
  <div class="d-flex justify-content-center mt-5" *ngIf="restartable">
    <button class="restart-button" (click)="restartStepper()">Restart Process</button>
  </div>


</div>

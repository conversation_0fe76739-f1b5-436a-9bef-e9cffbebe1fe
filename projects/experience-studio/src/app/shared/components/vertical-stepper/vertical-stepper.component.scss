.vertical-stepper {
  width: 100%;
  margin: 1rem auto;
  border-radius: 8px;
  animation: fadeIn 0.5s ease-in-out;

  &.light,
  &.dark {
    background-color: var(--chat-window-card-bg-color) !important;
    box-shadow: none;
    backdrop-filter: blur(60px);
  }

  .stepper-item {
    width: 100%;
    will-change: transform, opacity;
    transform: translateZ(0);

    &.hidden { display: none; }
    &.future { opacity: 0; height: 0; transform: translateY(-20px); overflow: hidden; }
    &.next { opacity: 0.6; pointer-events: none; }

    &.completed {
      .step-circle { background-color: #9c27b0; border: none; color: white; box-shadow: 0 2px 4px rgba(156, 39, 176, 0.3); }
      .step-line { background: linear-gradient(180deg, #9c27b0 0%, #e91e63 100%); }
      .step-title {
        .light & { color: var(--color-primary, #6b46c1); }
        .dark & { color: var(--color-primary-light, #9f7aea); }
      }
      &.in-progress-mode .step-circle, &.in-progress-mode .step-title { cursor: pointer !important; pointer-events: auto; }
    }

    &.active {
      .step-circle { border: none; color: white; box-shadow: none; }
      .step-title {
        font-weight: 600;
        .light & { color: var(--color-primary, #6b46c1); }
        .dark & { color: var(--color-primary-light, #9f7aea); }
      }
    }
  }


  .step-line-container {
    position: absolute; left: 11px; top: 30px; bottom: -20px; width: 2px; z-index: 0;
    height: calc(100% - 10px); will-change: height; transform: translateZ(0);
    .light & { background-color: #e9ecef; }
    .dark & { background-color: #555; }
    &.hidden-line { opacity: 0; visibility: hidden; }
  }

  .step-line {
    position: absolute; top: 0; width: 100%; height: 0;
    background: linear-gradient(180deg, #9c27b0 0%, #e91e63 100%);
    transition: height 2s cubic-bezier(0.4, 0, 0.2, 1);
    &.completed { height: 100%; }
    &.animating, &.expanding { animation: connectLine 1.5s cubic-bezier(0.4, 0, 0.2, 1) forwards; }
  }

  .step-circle {
    z-index: 10; width: 24px; height: 24px; border-radius: 50%; margin-right: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); position: relative;
    .light & { border: none; background-color: #f8f9fa; }
    .dark & { background-color: #333; border: none; }
    &.completed, &.active, &.failed { background-color: transparent !important; box-shadow: none; border: none; }
    &.failed, &.processing, &.clickable { cursor: pointer !important; }
    .stepper-item.in-progress-mode & { cursor: pointer !important; pointer-events: auto; }
  }


  .step-icon {
    width: 24px; height: 24px; position: absolute; top: 0; left: 0; z-index: 20;
    animation: fadeInScale 0.3s ease-out; filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
  }

  .step-number {
    font-weight: 600; font-size: 1rem;
    .light & { color: var(--text-secondary, #4a5568); }
    .dark & { color: var(--text-secondary-dark, #e2e8f0); }
  }

  .active .step-number {
    .light & { color: var(--color-primary, #6b46c1); }
    .dark & { color: var(--color-primary-light, #9f7aea); }
  }

  .step-content { flex: 1; padding-top: 4px; }

  .step-title {
    font-size: 1.125rem; font-weight: 600; margin-bottom: 0.5rem; cursor: pointer;
    .stepper-item.active:not(.completed) &, .stepper-item.in-progress-mode &, &.clickable {
      cursor: pointer !important; pointer-events: auto;
    }
    .light & { color: var(--text-primary, #2d3748); }
    .dark & { color: var(--text-primary-dark, #f8f9fa); }
    &:hover {
      .light & { color: var(--color-primary, #6b46c1); }
      .dark & { color: var(--color-primary-light, #9f7aea); }
    }
  }


  .step-retry-button {
    background-color: #7e3af2; border: none; border-radius: 50%; padding: 6px; width: 32px; height: 32px;
    cursor: pointer; margin-left: 0.5rem; color: white; z-index: 20; box-shadow: 0 2px 8px rgba(126, 58, 242, 0.4);
    &:hover {
      transform: translateY(-2px);
      .light & { background-color: #6929c4; box-shadow: 0 4px 12px rgba(126, 58, 242, 0.5); }
      .dark & { background-color: #8b5cf6; box-shadow: 0 4px 12px rgba(139, 92, 246, 0.5); }
      svg { animation: pulse-icon 1s infinite alternate; }
    }
    &:active { transform: translateY(0); animation: shake-button 0.5s ease-in-out; }
    &:focus { outline: none; }
    svg { width: 18px; height: 18px; fill: white; transform: rotate(-10deg); }
  }

  .step-description {
    font-size: 0.875rem; margin-bottom: 0.5rem; max-height: 1000px; opacity: 1; overflow: hidden;
    will-change: max-height, opacity, margin; transform: translateZ(0);
    .light & { color: var(--text-secondary, #4a5568); }
    .dark & { color: var(--text-secondary-dark, #cbd5e0); }
    &.collapsed { max-height: 0; opacity: 0; margin: 0; }
    ::ng-deep {
      p { margin: 0 0 0.5rem 0; }
      ul, ol { margin: 0.5rem 0; padding-left: 1.25rem; }
    }
  }

  .modern-loading-spinner {
    width: 24px; height: 24px; position: absolute; top: 0; left: 0;
    .spinner-ring {
      position: absolute; width: 24px; height: 24px; border-radius: 50%; border: 3px solid transparent;
      border-top-color: #6566cd; border-bottom-color: #e30a6d; filter: drop-shadow(0 0 1px rgba(101, 102, 205, 0.3));
      animation: spin-ring 1.5s ease-in-out infinite;
    }
    .spinner-core {
      width: 12px; height: 12px; border-radius: 50%; background: linear-gradient(135deg, #6566cd 0%, #e30a6d 100%);
      box-shadow: 0 0 10px rgba(229, 10, 109, 0.5); animation: pulse 1.5s ease-in-out infinite alternate;
    }
  }

  .step-next { width: 100%; pointer-events: none; }

  .next-title {
    font-size: 1.125rem; font-weight: 600; margin-bottom: 0.5rem;
    .light & { color: var(--text-primary, #2d3748); }
    .dark & { color: var(--text-primary-dark, #f8f9fa); }
  }


  .step-timer {
    margin-left: auto; padding: 4px 8px; border-radius: 4px; font-size: 0.75rem; font-weight: 500;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace; white-space: nowrap; flex-shrink: 0;
    backdrop-filter: blur(10px); animation: timerFadeIn 0.3s ease-out;
    .light & { background: rgba(0, 0, 0, 0.85); color: #ffffff; border: 1px solid rgba(255, 255, 255, 0.15); }
    .dark & { background: rgba(0, 0, 0, 0.9); color: #ffffff; border: 1px solid rgba(255, 255, 255, 0.2); }
    &.completed {
      opacity: 0.8;
      .light & { background: rgba(34, 197, 94, 0.15); color: #16a34a; border: 1px solid rgba(34, 197, 94, 0.3); }
      .dark & { background: rgba(34, 197, 94, 0.2); color: #4ade80; border: 1px solid rgba(34, 197, 94, 0.4); }
    }
  }

  .restart-button {
    padding: 0.75rem 1.5rem; background-color: #6b46c1; color: white; font-weight: 600;
    border: none; border-radius: 4px; cursor: pointer;
    &:hover { background-color: #553c9a; }
  }


  // Animations & Utilities
  @keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
  @keyframes fadeInScale { from { opacity: 0; transform: scale(0.8); } to { opacity: 1; transform: scale(1); } }
  @keyframes connectLine { 0% { height: 0; } 100% { height: 100%; } }
  @keyframes spin-ring { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
  @keyframes pulse { 0% { transform: scale(0.8); opacity: 0.7; } 100% { transform: scale(1); opacity: 1; } }
  @keyframes pulse-icon { 0% { transform: rotate(-10deg) scale(1); } 100% { transform: rotate(-10deg) scale(1.2); } }
  @keyframes shake-button { 0%, 100% { transform: translateX(0); } 10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); } 20%, 40%, 60%, 80% { transform: translateX(2px); } }
  @keyframes timerFadeIn { from { opacity: 0; transform: scale(0.9); } to { opacity: 1; transform: scale(1); } }
  @keyframes shimmer { 0% { background-position: 200% 0; } 100% { background-position: -200% 0; } }

  .step-circle.active .modern-loading-spinner { top: 0; left: 0; right: 0; bottom: 0; margin: auto; }
  .step-circle.failed .modern-loading-spinner { display: none; }
  .step-title.typing .step-title-text { transition: all 0.1s ease-out; }
  .step-description.typing ::ng-deep p { display: inline; transition: all 0.05s ease-out; }
  .step-description.shimmer ::ng-deep p, .step-description.shimmer ::ng-deep li, .step-description.shimmer ::ng-deep code, .step-description.shimmer ::ng-deep pre {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 100%);
    background-size: 200% 100%; animation: shimmer 2s infinite; background-clip: text; -webkit-background-clip: text; color: transparent;
    .light & { text-shadow: 0 0 0 var(--text-secondary, #4a5568); }
    .dark & { text-shadow: 0 0 0 var(--text-secondary-dark, #cbd5e0); }
  }
}


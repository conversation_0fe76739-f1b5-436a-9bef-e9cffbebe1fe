import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
  OnDestroy,
  Output,
  EventEmitter,
  ChangeDetectorRef,
  ChangeDetectionStrategy,
  inject,
  DestroyRef,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MarkdownModule } from 'ngx-markdown';
import { finalize } from 'rxjs/operators';
import { StepperState, StepperStateDisplayTitles } from '../../models/stepper-states.enum';
import { PollingService } from '../../services/polling.service';
import { ErrorReportingService } from '../../services/error-reporting.service';
import { createLogger } from '../../utils/logger';
import { ContentSanitizationService } from '../../services/content-sanitization.service';

export interface StepperItem {
  title: string;
  description: string;
  visibleTitle: string; // Property to track the visible portion of the title for typewriter effect
  visibleDescription: string; // Property to track the visible portion of the description for typewriter effect
  completed: boolean;
  active: boolean;

  collapsed?: boolean; // Property to track collapsed state
  isTyping?: boolean; // Property to track if the description is currently being typed
  isTitleTyping?: boolean; // Property to track if the title is currently being typed
  retryCount?: number; // Property to track retry attempts for failed steps
  isRetrying?: boolean; // Property to track if the step is currently being retried (for shimmer effect)

  // Timer properties
  startTime?: number; // Timestamp when the step started
  elapsedTime?: number; // Current elapsed time in seconds
  timerActive?: boolean; // Whether the timer is currently running
  completionTime?: number; // Final time when the step was completed (in seconds)
}

@Component({
  selector: 'app-vertical-stepper',
  standalone: true,
  imports: [CommonModule, MarkdownModule],
  templateUrl: './vertical-stepper.component.html',
  styleUrls: ['./vertical-stepper.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VerticalStepperComponent implements OnChanges, OnInit, OnDestroy {
  @Input() progress: string = '';
  @Input() progressDescription: string = '';
  @Input() status: string = 'PENDING'; // PENDING, IN_PROGRESS, COMPLETED, FAILED
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() restartable: boolean = false; // Whether to show restart button
  @Input() projectId: string = ''; // Project ID for API integration
  @Input() jobId: string = ''; // Job ID for API integration
  @Input() useApi: boolean = false; // Whether to use API for data or local inputs
  @Output() stepUpdated = new EventEmitter<number>(); // Emits the current step index when it changes
  @Output() retryStep = new EventEmitter<number>(); // Emits the step index when retry is clicked

  private logger = createLogger('VerticalStepperComponent');

  steps: StepperItem[] = [];
  currentStep: StepperItem | null = null;
  currentStepIndex: number = 0;
  animatingLine: boolean = false;
  private timeoutRefs: { [key: string]: any } = {};
  // private subscriptionManager = new SubscriptionManager();
  private typingSpeed: number = 15; // Slightly slower typewriter effect for better readability

  // Track the single user-expanded step (apart from processing step)
  private userExpandedStep: number | null = null;

  // Timer-related properties
  private timerInterval: any;
  private timerUpdateInterval: number = 1000; // Update timer every second

  // Track which steps are collapsed
  private collapsedSteps: Set<number> = new Set();

  // Map of step states to their display titles
  private stepperStateMap = StepperStateDisplayTitles;

  // Expose StepperState enum to the template
  public StepperState = StepperState;

  private readonly errorReportingService = inject(ErrorReportingService);
  private readonly destroyRef = inject(DestroyRef);

  constructor(
    private pollingService: PollingService,
    private cdr: ChangeDetectorRef,
    private contentSanitizationService: ContentSanitizationService
  ) {}

  ngOnInit(): void {
    // If using API and we have project and job IDs, start polling
    if (this.useApi && this.projectId && this.jobId) {
      this.startApiPolling();
    }
    // Otherwise, initialize with default step if none exists and we have progress
    else if (this.steps.length === 0 && this.progress) {
      const displayTitle = this.getDisplayTitleForProgress(this.progress);
      const description = this.formatDescription(this.progressDescription) || 'Starting process...';
      this.steps.push({
        title: displayTitle,
        description: description,
        visibleTitle: '', // Start empty for typewriter effect
        visibleDescription: '', // Start empty for typewriter effect
        completed: false,
        active: true,
        isTitleTyping: true,
        isTyping: true,
        startTime: Date.now(),
        elapsedTime: 0,
        timerActive: true
      });
      this.currentStep = this.steps[0];
      this.currentStepIndex = 0;

      // Ensure processing step is always expanded
      if (this.status === 'IN_PROGRESS') {
        // Keep the processing step expanded
        this.collapsedSteps.delete(this.currentStepIndex);
      }

      // Emit the step updated event
      this.stepUpdated.emit(this.currentStepIndex);

      // Start the typewriter animation for the initial step after a short delay
      // to ensure the component is fully rendered
      setTimeout(() => {
        this.startTypewriterAnimation(this.currentStepIndex);
      }, 100);

      // Start the timer for the initial step
      this.startTimer();
    }

    // Initialize any existing steps that might have been passed in
    for (let i = 0; i < this.steps.length; i++) {
      const step = this.steps[i];
      if (!step.visibleDescription || !step.visibleTitle) {
        step.visibleTitle = step.visibleTitle || '';
        step.visibleDescription = step.visibleDescription || '';
        step.isTitleTyping = true;
        step.isTyping = true;

        // Start typewriter animation for each step with a staggered delay
        setTimeout(() => {
          this.startTypewriterAnimation(i);
        }, 100 * (i + 1));
      }
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    // If using API and project or job ID changes, restart polling
    if (this.useApi && (changes['projectId'] || changes['jobId'])) {
      if (this.projectId && this.jobId) {
        // Stop existing polling if any
        this.stopApiPolling();
        // Start new polling
        this.startApiPolling();
      }
    }
    // When not using API and progress or description changes, update the stepper
    else if (!this.useApi && (changes['progress'] || changes['progressDescription'] || changes['status'])) {
      this.updateStepper();
    }

    // Special handling for progress description changes in failed state
    if (changes['progressDescription'] && this.status === 'FAILED') {
      this.updateFailedStepDescription();
    }
  }

  /**
   * Updates the description of the failed step when the progress description changes
   * This ensures that the error message is always up to date
   */
  private updateFailedStepDescription(): void {
    // Find the failed step
    const failedStepIndex = this.steps.findIndex(step => this.isFailureStep(step));
    if (failedStepIndex >= 0) {
      // Extract error message from the progress description or log field
      let errorMessage = '';

      // First try to get the error message from the progress description
      if (this.progressDescription && this.progressDescription.trim() !== '') {
        errorMessage = this.formatDescription(this.progressDescription);
      }

      // If we couldn't get an error message from the progress description,
      // try to get it from the polling service's last status response
      if (!errorMessage && this.useApi) {
        const lastResponse = this.pollingService.getLastStatusResponse();
        if (lastResponse && lastResponse.details && lastResponse.details.log) {
          // Extract error message from the log field
          const logContent = lastResponse.details.log;
          errorMessage = this.extractErrorMessage(logContent);
        }
      }

      // If we have an error message, update the failed step description
      if (errorMessage) {
        const failedStep = this.steps[failedStepIndex];

        // Only update if the description has changed
        if (failedStep.description !== errorMessage) {
          failedStep.description = errorMessage;
          failedStep.visibleTitle = ''; // Reset visible title
          failedStep.visibleDescription = ''; // Reset visible description
          failedStep.isTitleTyping = true; // Set title typing state
          failedStep.isTyping = true; // Set typing state

          // Start typewriter animation
          this.startTypewriterAnimation(failedStepIndex);

          // Trigger change detection for OnPush
          this.cdr.markForCheck();
        }
      }
    }
  }

  ngOnDestroy(): void {
    // Clear all timeouts when component is destroyed
    this.clearAllTimeouts();
    // Stop timer
    this.stopTimer();
    // Stop polling if active
    this.stopApiPolling();
    // Clear user-expanded tracking
    this.userExpandedStep = null;
  }

  /**
   * Start polling the API for status updates
   */
  private startApiPolling(): void {
    if (!this.projectId || !this.jobId) {
      return;
    }

    // Start polling with the polling service
    this.pollingService.startPolling(this.projectId, this.jobId);

    // Subscribe to status updates
    this.pollingService.status$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((status: string) => {
        this.status = status;
        this.cdr.markForCheck(); // Trigger change detection for OnPush
      });

    // Subscribe to progress updates
    this.pollingService.progress$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((progress: string) => {
        if (progress) {
          this.progress = progress;
          this.updateStepper();
          this.cdr.markForCheck(); // Trigger change detection for OnPush
        }
      });

    // Subscribe to progress description updates
    this.pollingService.progressDescription$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((description: string) => {
        if (description) {
          this.progressDescription = description;
          this.updateStepper();
          this.cdr.markForCheck(); // Trigger change detection for OnPush
        }
      });
  }

  /**
   * Stop polling the API
   */
  private stopApiPolling(): void {
    this.pollingService.stopPolling();
  }

  // Clear all timeouts
  private clearAllTimeouts(): void {
    Object.values(this.timeoutRefs).forEach(timeoutId => clearTimeout(timeoutId));
    this.timeoutRefs = {};
  }

  /**
   * Clean up user-expanded tracking for invalid step indices
   */
  private cleanupUserExpandedTracking(): void {
    // Remove tracked index if it's beyond the current steps length
    if (this.userExpandedStep !== null && this.userExpandedStep >= this.steps.length) {
      this.userExpandedStep = null;
    }
  }

  /**
   * Format description to ensure proper markdown and extract error messages from JSON if needed
   * @param description The description to format
   * @returns The formatted description
   */
  private formatDescription(description?: string): string {
    if (!description) return '';

    // Use the content sanitization service to preprocess the description
    // This will handle <mlo_artifact> tags, JSON error messages, and HTML sanitization
    return this.contentSanitizationService.preprocessStepperDescription(description);
  }

  // Format title to replace underscores with spaces
  formatTitle(title: string): string {
    return title.replace(/_/g, ' ');
  }

  /**
   * Gets sanitized description for safe markdown rendering
   * @param description The description to sanitize
   * @returns Sanitized description safe for markdown rendering
   */
  getSanitizedDescription(description: string): string {
    return this.contentSanitizationService.sanitizeForMarkdown(description);
  }

  // Get the status of a step
  getStepStatus(index: number): string {
    if (index < this.currentStepIndex) return 'completed';
    if (index === this.currentStepIndex) return 'active';
    if (index === this.currentStepIndex + 1) return 'next';
    return 'future';
  }

  // Consolidated helper methods for template simplification
  getStepClasses(index: number): string[] {
    const classes = [this.getStepStatus(index)];
    if (this.isStepCollapsed(index)) classes.push('collapsed');
    if (this.status === 'IN_PROGRESS' && this.isProcessingStep(index)) classes.push('in-progress-mode');
    return classes;
  }

  getCircleClasses(step: StepperItem, index: number): string[] {
    const classes = ['cursor-pointer'];
    const status = this.getStepStatus(index);
    const isFailure = this.isFailureStep(step);

    if (status === 'active' && !step.completed && !isFailure) classes.push('active');
    if (isFailure || (this.status === 'FAILED' && index === this.currentStepIndex)) classes.push('failed');
    if (this.status === 'IN_PROGRESS' && this.isProcessingStep(index)) classes.push('processing');

    return classes;
  }

  isStepCompleted(index: number): boolean {
    return this.getStepStatus(index) === 'completed';
  }

  private isFailedBuildStep(step: StepperItem): boolean {
    return step.title === this.getDisplayTitleForProgress(StepperState.BUILD_FAILED);
  }

  showSuccessIcon(step: StepperItem, index: number): boolean {
    return (this.isStepCompleted(index) || step.completed) &&
           !this.isFailureStep(step) &&
           !this.isFailedBuildStep(step);
  }

  showErrorIcon(step: StepperItem, index: number): boolean {
    return this.isFailureStep(step) ||
           (this.status === 'FAILED' && index === this.currentStepIndex) ||
           this.isFailedBuildStep(step);
  }

  showLoadingSpinner(step: StepperItem, index: number): boolean {
    return this.getStepStatus(index) === 'active' &&
           !step.completed &&
           this.status !== 'FAILED' &&
           !this.isFailureStep(step) &&
           !this.isFailedBuildStep(step);
  }

  showStepNumber(step: StepperItem, index: number): boolean {
    const status = this.getStepStatus(index);
    return status !== 'completed' && status !== 'active' && !step.completed;
  }

  showTimer(step: StepperItem): boolean {
    return (step.timerActive && step.elapsedTime !== undefined) ||
           (step.completionTime !== undefined);
  }

  // Additional helper methods for optimized template
  getStepIconType(step: StepperItem, index: number): string {
    if (this.showSuccessIcon(step, index)) return 'success';
    if (this.showErrorIcon(step, index)) return 'error';
    if (this.showLoadingSpinner(step, index)) return 'loading';
    return 'number';
  }

  getStepTitle(step: StepperItem): string {
    return this.formatTitle(step.visibleTitle || step.title);
  }

  isTimerCompleted(step: StepperItem): boolean {
    return step.completionTime !== undefined && !step.timerActive;
  }

  getFormattedTime(step: StepperItem): string {
    return this.formatElapsedTime(step.completionTime || step.elapsedTime || 0);
  }

  getRetryTitle(step: StepperItem): string {
    return `Retry attempt ${step.retryCount || 0}/3`;
  }

  getDescriptionClasses(step: StepperItem, index: number): string[] {
    const classes = [];
    if (this.isStepCollapsed(index)) classes.push('collapsed');
    if (step.isTyping) classes.push('typing');
    if (step.isRetrying) classes.push('shimmer');
    return classes;
  }

  getStepDescription(step: StepperItem): string {
    return this.getSanitizedDescription(step.visibleDescription || step.description);
  }

  // Check if a step should be shown
  shouldShowStep(index: number): boolean {
    // Show current step and next step title only
    // Current step and all completed steps
    if (index <= this.currentStepIndex) {
      return true;
    }

    // Next step (show with low opacity)
    if (index === this.currentStepIndex + 1) {
      return true;
    }

    // Hide all future steps beyond the next one
    return false;
  }

  // Check if a step's line should be animating
  isLineAnimating(index: number): boolean {
    return this.animatingLine && index === this.currentStepIndex - 1;
  }

  // Check if a step is collapsed
  isStepCollapsed(index: number): boolean {
    return this.collapsedSteps.has(index);
  }

  /**
   * Check if we're at the final deployment stage
   * Only stop stepper when we reach DEPLOYED/DEPLOY progress state
   */
  private isFinalDeploymentStage(): boolean {
    const progressUpper = this.progress.toUpperCase();

    const isFinalState = (
      progressUpper === 'DEPLOYED' ||
      progressUpper === 'DEPLOY'
    );

    // this.logger.info(`🔍 Checking if at final deployment stage:`, {
    //   progress: this.progress,
    //   status: this.status,
    //   isFinalState: isFinalState
    // });

    return isFinalState;
  }

  // Toggle the collapsed state of a step
  toggleStepCollapse(index: number): void {
    const isCurrentlyCollapsed = this.collapsedSteps.has(index);
    const isProcessingStep = this.status === 'IN_PROGRESS' && index === this.currentStepIndex;

    if (isCurrentlyCollapsed) {
      // User is expanding a step

      // Smart collapse: Collapse all other steps except processing step and the one being expanded
      for (let i = 0; i < this.steps.length; i++) {
        if (i !== index && !(this.status === 'IN_PROGRESS' && i === this.currentStepIndex)) {
          this.collapsedSteps.add(i);
        }
      }

      // Expand the clicked step
      this.collapsedSteps.delete(index);

      // Track this as user-expanded (unless it's the processing step)
      if (!isProcessingStep) {
        this.userExpandedStep = index;
      }

      // If the step is being expanded and hasn't completed its typewriter animation,
      // restart the animation if it was interrupted
      const step = this.steps[index];
      if (step && step.description && (!step.visibleDescription || step.visibleDescription.length < step.description.length)) {
        step.isTyping = true;
        this.startTypewriterAnimation(index);
      }
    } else {
      // User is collapsing a step
      this.collapsedSteps.add(index);

      // Remove from user-expanded tracking
      if (this.userExpandedStep === index) {
        this.userExpandedStep = null;
      }
    }

    // Trigger change detection for OnPush
    this.cdr.markForCheck();
  }



  // Restart the stepper
  restartStepper(): void {
    this.clearAllTimeouts();
    this.steps = [];
    this.currentStepIndex = 0;
    this.currentStep = null;
    this.animatingLine = false;
    this.collapsedSteps.clear();

    // If using API, restart polling
    if (this.useApi && this.projectId && this.jobId) {
      this.stopApiPolling();
      this.startApiPolling();
    }
    // Otherwise, if we have a progress value, initialize with it
    else if (this.progress) {
      const displayTitle = this.getDisplayTitleForProgress(this.progress);
      const description = this.formatDescription(this.progressDescription) || 'Starting process...';
      this.steps.push({
        title: displayTitle,
        description: description,
        visibleTitle: '', // Start empty for typewriter effect
        visibleDescription: '', // Start empty for typewriter effect
        completed: false,
        active: true,
        isTitleTyping: true,
        isTyping: true,
        startTime: Date.now(),
        elapsedTime: 0,
        timerActive: true
      });
      this.currentStep = this.steps[0];

      // Ensure only the first step is expanded
      // First, collapse all steps except the processing step
      const processingStepIndex = (this.status === 'IN_PROGRESS' || this.status === 'in-progress') ? 0 : -1;
      for (let i = 0; i < this.steps.length; i++) {
        // Don't collapse the processing step
        if (i !== processingStepIndex) {
          this.collapsedSteps.add(i);
        }
      }
      // Then expand only the first step
      this.collapsedSteps.delete(0);

      // Emit the step updated event
      this.stepUpdated.emit(0);

      // Start the typewriter animation for the restarted step
      this.startTypewriterAnimation(0);

      // Start the timer for the restarted step
      this.startTimer();
    }

    // Trigger change detection for OnPush
    this.cdr.markForCheck();
  }

  /**
   * Completely resets the stepper state
   * This is a public method that can be called from parent components
   * to reset the stepper when starting a new project
   */
  public resetStepper(): void {
    // Clear all timeouts to prevent any pending animations
    this.clearAllTimeouts();

    // Reset all state variables
    this.steps = [];
    this.currentStepIndex = 0;
    this.currentStep = null;
    this.animatingLine = false;
    this.collapsedSteps.clear();

    // Reset input properties
    this.progress = '';
    this.progressDescription = '';
    this.status = 'PENDING';

    // Stop any API polling
    if (this.useApi) {
      this.stopApiPolling();
    }

    // Emit an event to notify parent components
    this.stepUpdated.emit(-1); // -1 indicates a complete reset

    // Trigger change detection for OnPush
    this.cdr.markForCheck();
  }

  private updateStepper(): void {
    if (!this.progress?.trim()) return;

    // Handle special cases first
    if (this.progress === StepperState.BUILD_FAILED || this.status === 'FAILED' || this.status === 'failed') {
      this.handleFailedStep(this.progress, this.progressDescription);
      return;
    }

    const displayTitle = this.getDisplayTitleForProgress(this.progress);
    const existingStepIndex = this.findExistingStep(displayTitle);

    if (existingStepIndex === -1) {
      this.addNewStep(displayTitle);
    } else {
      this.updateExistingStep(existingStepIndex, displayTitle);
    }

    // Handle status completion
    this.handleStatusCompletion();
  }

  private findExistingStep(displayTitle: string): number {
    return this.steps.findIndex(step => step.title === displayTitle || step.title === this.progress);
  }

  private addNewStep(displayTitle: string): void {
    const newStep = this.createStepItem(displayTitle);

    if (this.steps.length > 0) {
      this.completePreviousStep();
    }

    this.animateLineConnection();
    this.steps.push(newStep);
    this.setCurrentStep(newStep, this.steps.length - 1);
    this.startStepAnimations();
    this.scrollToNewStep();
  }

  private createStepItem(title: string): StepperItem {
    return {
      title,
      description: this.formatDescription(this.progressDescription) || '',
      visibleTitle: '',
      visibleDescription: '',
      completed: false,
      active: true,
      isTitleTyping: true,
      isTyping: true,
      startTime: Date.now(),
      elapsedTime: 0,
      timerActive: true
    };
  }

  private completePreviousStep(): void {
    const prevStepIndex = this.steps.length - 1;
    const prevStep = this.steps[prevStepIndex];

    Object.assign(prevStep, { completed: true, active: false });
    this.stopStepTimer(prevStepIndex);
    this.applySmartCollapse(prevStepIndex);
  }

  private applySmartCollapse(excludeIndex?: number): void {
    this.steps.forEach((_, i) => {
      if (i !== excludeIndex && this.userExpandedStep !== i && i !== this.currentStepIndex) {
        this.collapsedSteps.add(i);
      }
    });
  }

  private animateLineConnection(): void {
    this.animatingLine = true;
    this.timeoutRefs['line-animation'] = setTimeout(() => this.animatingLine = false, 2500);
  }

  private setCurrentStep(step: StepperItem, index: number): void {
    this.currentStep = step;
    this.currentStepIndex = index;
    this.collapsedSteps.delete(index);
    this.stepUpdated.emit(index);
  }

  private startStepAnimations(): void {
    this.startTypewriterAnimation(this.currentStepIndex);
    if (!this.timerInterval) this.startTimer();
  }

  private scrollToNewStep(): void {
    setTimeout(() => {
      const lastStep = document.querySelector('.stepper-item:last-child') as HTMLElement;
      lastStep?.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }, 100);
  }

  private updateExistingStep(index: number, displayTitle: string): void {
    const step = this.steps[index];

    if (this.shouldUpdateDescription(step)) {
      this.updateStepDescription(step, index);
    }

    step.title = displayTitle;
    this.setCurrentStep(step, index);
    this.updateStepStates(index);
    this.applySmartCollapse();
  }

  private shouldUpdateDescription(step: StepperItem): boolean {
    if (!this.progressDescription?.trim()) return false;
    const newDescription = this.formatDescription(this.progressDescription);
    return step.description !== newDescription;
  }

  private updateStepDescription(step: StepperItem, index: number): void {
    step.description = this.formatDescription(this.progressDescription);
    step.visibleTitle = '';
    step.visibleDescription = '';
    step.isTitleTyping = true;
    step.isTyping = true;
    this.startTypewriterAnimation(index);
  }

  private updateStepStates(activeIndex: number): void {
    this.steps.forEach((step, i) => {
      step.active = i === activeIndex;
      step.completed = i < activeIndex;
    });
  }

  private handleStatusCompletion(): void {
    if ((this.status === 'COMPLETED' || this.status === 'completed') && this.isFinalDeploymentStage()) {
      this.handleFinalCompletion();
    } else if ((this.status === 'COMPLETED' || this.status === 'completed') && !this.isFinalDeploymentStage()) {
      this.handleStepCompletion();
    } else if (this.status === 'FAILED' || this.status === 'failed') {
      this.handleFailedStep(this.progress, this.progressDescription);
    }
    this.cleanupUserExpandedTracking();
  }

  private handleFinalCompletion(): void {
    this.steps.forEach(step => this.completeStep(step));
    if (this.progress === StepperState.BUILD_SUCCEEDED) {
      this.handleBuildSucceeded();
    }
  }

  private handleStepCompletion(): void {
    if (this.currentStep) this.completeStep(this.currentStep);
  }

  private completeStep(step: StepperItem): void {
    Object.assign(step, { completed: true, active: false });
    if (step.timerActive && step.startTime) {
      const completionTime = Math.floor((Date.now() - step.startTime) / 1000);
      Object.assign(step, { completionTime, elapsedTime: completionTime, timerActive: false });
    }
  }

  private handleBuildSucceeded(): void {
    const buildSucceededStepIndex = this.steps.findIndex(
      step => step.title === this.getDisplayTitleForProgress(StepperState.BUILD_SUCCEEDED)
    );

    if (buildSucceededStepIndex === -1 && this.steps.length > 0) {
      const buildSucceededStep = this.createBuildSucceededStep();
      this.addFinalStep(buildSucceededStep);
    } else if (buildSucceededStepIndex !== -1) {
      this.updateBuildSucceededStep(buildSucceededStepIndex);
    }
  }

  private createBuildSucceededStep(): StepperItem {
    return {
      title: this.getDisplayTitleForProgress(StepperState.BUILD_SUCCEEDED),
      description: this.formatDescription(this.progressDescription) || 'Build completed successfully.',
      visibleTitle: '',
      visibleDescription: '',
      completed: true,
      active: true,
      isTitleTyping: true,
      isTyping: true,
      startTime: Date.now(),
      elapsedTime: 0,
      timerActive: false
    };
  }

  private updateBuildSucceededStep(index: number): void {
    const step = this.steps[index];
    step.completed = true;
    step.active = true;

    if (this.progressDescription?.trim()) {
      const newDescription = this.formatDescription(this.progressDescription);
      if (step.description !== newDescription) {
        step.description = newDescription;
        step.visibleTitle = '';
        step.visibleDescription = '';
        step.isTitleTyping = true;
        step.isTyping = true;
        this.startTypewriterAnimation(index);
      }
    }

    this.setCurrentStep(step, index);
    this.applySmartCollapse();
    this.stepUpdated.emit(index);
  }



  /**
   * Handles a failed step, creating or updating it as needed
   * @param progress The progress state that failed
   * @param progressDescription The description of the failure
   */
  private handleFailedStep(progress: string, progressDescription: string): void {
    // First, determine the appropriate failure step title based on the current progress
    let failedStepTitle = '';
    let defaultFailureMessage = 'Process failed.';

    // If it's a build failure or already BUILD_FAILED, use the BUILD_FAILED state
    if (progress === StepperState.BUILD_STARTED || progress === StepperState.BUILD_FAILED) {
      failedStepTitle = this.getDisplayTitleForProgress(StepperState.BUILD_FAILED);
      defaultFailureMessage = 'Build process failed.';
    } else {
      // For other steps, create a custom failure title based on the current step
      // First get the current step's display title
      const currentStepTitle = this.getDisplayTitleForProgress(progress);
      failedStepTitle = `${currentStepTitle} Failed`;
    }

    // For BUILD_FAILED progress, we always want to create a new step
    // This ensures we show "Build Failed" as a separate step with an X icon
    let failedStepIndex = -1;

    // Only look for existing failure steps if it's not BUILD_FAILED progress
    if (progress !== StepperState.BUILD_FAILED) {
      // Check if we already have a failure step
      failedStepIndex = this.steps.findIndex(
        step => step.title === failedStepTitle || this.isFailureStep(step)
      );
    }

    // Set the status to FAILED to ensure the retry button is displayed
    this.status = 'FAILED';

    // Extract error message from the log field if available
    let errorMessage = '';

    // First try to get the error message from the progress description
    if (progressDescription && progressDescription.trim() !== '') {
      errorMessage = this.formatDescription(progressDescription);
    }

    // If we couldn't get an error message from the progress description,
    // try to get it from the polling service's last status response
    if (!errorMessage && this.useApi) {
      const lastResponse = this.pollingService.getLastStatusResponse();
      if (lastResponse && lastResponse.details && lastResponse.details.log) {
        // Extract error message from the log field
        const logContent = lastResponse.details.log;
        errorMessage = this.extractErrorMessage(logContent);
      }
    }

    // If we still don't have an error message, use the default
    const formattedDescription = errorMessage || defaultFailureMessage;

    if (failedStepIndex === -1) {
      // We don't have a failure step yet, so create one
      const failedStep: StepperItem = {
        title: failedStepTitle,
        description: formattedDescription, // Use the formatted description
        visibleTitle: '', // Start empty for typewriter effect
        visibleDescription: '', // Start empty for typewriter effect
        completed: false,
        active: true,
        isTitleTyping: true,
        isTyping: true,
        retryCount: 0, // Initialize retry count for failed steps
        startTime: Date.now(),
        elapsedTime: 0,
        timerActive: false // Failed steps don't need active timers
      };

      // If we have a current step, mark it appropriately
      if (this.currentStep) {
        this.currentStep.active = false;
        // Ensure the step is not marked as completed to prevent showing checkmark
        this.currentStep.completed = false;

        // Also ensure that all previous steps are properly marked
        for (let i = 0; i < this.steps.length; i++) {
          // If this is the current step or a step that was in progress when failure occurred,
          // mark it as not completed
          if (i === this.currentStepIndex ||
              (this.steps[i].active && !this.steps[i].completed)) {
            this.steps[i].completed = false;
            this.steps[i].active = false;
          }
        }
      }

      // Smart collapse: Collapse steps that are not user-expanded
      for (let i = 0; i < this.steps.length; i++) {
        if (this.userExpandedStep !== i) {
          this.collapsedSteps.add(i);
        }
      }

      // Add the failed step
      this.steps.push(failedStep);
      this.currentStep = failedStep;
      this.currentStepIndex = this.steps.length - 1;

      // Ensure the failed step is expanded
      this.collapsedSteps.delete(this.currentStepIndex);

      // Start the typewriter animation for the failed step
      this.startTypewriterAnimation(this.currentStepIndex);

      this.stepUpdated.emit(this.currentStepIndex);

      // Ensure smooth scrolling to the failed step
      setTimeout(() => {
        const stepElements = document.querySelectorAll('.stepper-item');
        if (stepElements && stepElements.length > 0) {
          const lastStep = stepElements[stepElements.length - 1] as HTMLElement;
          if (lastStep) {
            lastStep.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
          }
        }
      }, 100);
    } else {
      // We already have a failure step, make sure it has the correct description
      const failedStep = this.steps[failedStepIndex];

      // Always update the description for failed steps to ensure it shows the latest error
      failedStep.description = formattedDescription;
      failedStep.visibleTitle = ''; // Reset visible title
      failedStep.visibleDescription = ''; // Reset visible description
      failedStep.isTitleTyping = true; // Set title typing state
      failedStep.isTyping = true; // Set typing state

      // Make this step active
      failedStep.active = true;
      failedStep.completed = false;

      // Update current step reference
      this.currentStep = failedStep;
      this.currentStepIndex = failedStepIndex;

      // Smart collapse: Only collapse steps that are not user-expanded
      for (let i = 0; i < this.steps.length; i++) {
        if (i !== failedStepIndex && this.userExpandedStep !== i) {
          this.collapsedSteps.add(i);
        }
      }

      // Ensure the failed step is expanded
      this.collapsedSteps.delete(failedStepIndex);

      // Start typewriter animation
      this.startTypewriterAnimation(failedStepIndex);

      // Emit step updated event
      this.stepUpdated.emit(failedStepIndex);
    }
  }

  /**
   * Gets the display title for a progress state
   * @param progress The progress state
   * @returns The display title for the progress state
   */
  public getDisplayTitleForProgress(progress: string): string {
    // Check if this is a known stepper state
    if (Object.values(StepperState).includes(progress as StepperState)) {
      return this.stepperStateMap[progress as StepperState];
    }

    // If not a known state, return the original progress value
    return progress;
  }

  /**
   * Determines if a step is a failure step
   * @param step The step to check
   * @returns True if the step is a failure step, false otherwise
   */
  isFailureStep(step: StepperItem): boolean {
    // Check if the step title contains 'Failed' or matches the BUILD_FAILED display title
    // Also check if the status is FAILED and this is the current step
    return step.title.includes('Failed') ||
           step.title === this.getDisplayTitleForProgress(StepperState.BUILD_FAILED) ||
           ((this.status === 'FAILED' || this.status === 'failed') && step === this.currentStep);
  }

  /**
   * Handles retry button click for a failed step
   * @param index The index of the step to retry
   */
  onRetryClick(index: number, event?: Event): void {
    // Stop event propagation if provided
    if (event) {
      event.stopPropagation();
    }

    // Get the step
    const step = this.steps[index];

    // Initialize retryCount if it doesn't exist
    if (step.retryCount === undefined) {
      step.retryCount = 0;
    }

    // Increment retry count
    step.retryCount++;

    this.logger.info('Retry button clicked', {
      stepIndex: index,
      stepTitle: step.title,
      retryCount: step.retryCount,
      progress: this.progress,
      status: this.status
    });

    // Check if this is a BUILD or DEPLOY failure and we need to report the error
    const isBuildOrDeployFailure = this.progress === StepperState.BUILD ||
                                  this.progress === StepperState.BUILD_FAILED ||
                                  this.progress === StepperState.DEPLOY ||
                                  this.progress === StepperState.DEPLOYED ||
                                  step.title.toLowerCase().includes('build') ||
                                  step.title.toLowerCase().includes('deploy');



    // Debug logging to identify why condition might fail
    this.logger.info('🔍 Retry condition evaluation:', {
      isBuildOrDeployFailure,
      progress: this.progress,
      status: this.status,
      statusTrimmed: this.status?.trim().toUpperCase(),
      useApi: this.useApi,
      hasProjectId: !!this.projectId,
      hasJobId: !!this.jobId,
      projectId: this.projectId,
      jobId: this.jobId,
      stepTitle: step.title
    });

    if (isBuildOrDeployFailure && (this.status?.trim().toUpperCase() === 'FAILED' || this.status?.trim().toLowerCase() === 'failed') && this.useApi && this.projectId && this.jobId) {
      this.logger.info('✅ Triggering BUILD/DEPLOY retry with error reporting');
      this.handleBuildRetryWithErrorReporting(index, step);
    } else {
      this.logger.warn('❌ Using regular retry instead of error reporting retry', {
        reason: !isBuildOrDeployFailure ? 'Not BUILD/DEPLOY failure' :
                this.status?.trim().toUpperCase() !== 'FAILED' ? `Status not FAILED (actual: '${this.status}')` :
                !this.useApi ? 'useApi is false' :
                !this.projectId ? 'No projectId' :
                !this.jobId ? 'No jobId' : 'Unknown reason'
      });
      this.handleRegularRetry(index, step);
    }
  }

  /**
   * Handle retry for BUILD and DEPLOY failures with error reporting
   * @param index The step index
   * @param step The step being retried
   */
  private handleBuildRetryWithErrorReporting(index: number, step: StepperItem): void {
    this.logger.info('🚀 Handling BUILD/DEPLOY retry with error reporting', {
      projectId: this.projectId,
      jobId: this.jobId,
      stepTitle: step.title,
      progress: this.progress,
      status: this.status,
      useApi: this.useApi,
      hasProjectId: !!this.projectId,
      hasJobId: !!this.jobId,
      projectIdLength: this.projectId?.length || 0,
      jobIdLength: this.jobId?.length || 0
    });

    // Extract error message from polling service
    const errorMessage = this.pollingService.getErrorMessageFromLastResponse();
    const lastResponse = this.pollingService.getLastStatusResponse();

    this.logger.info('🔍 Error message extraction result:', {
      hasErrorMessage: !!errorMessage,
      errorMessageLength: errorMessage?.length || 0,
      errorMessagePreview: errorMessage?.substring(0, 100) || 'No message',
      hasLastResponse: !!lastResponse,
      lastResponseKeys: lastResponse ? Object.keys(lastResponse) : [],
      lastResponseDetails: lastResponse?.details ? Object.keys(lastResponse.details) : [],
      lastResponseLog: lastResponse?.details?.log ? 'Has log field' : 'No log field'
    });

    if (!errorMessage) {
      this.logger.warn('❌ No error message found for BUILD/DEPLOY retry, proceeding without error reporting');
      this.handleRegularRetry(index, step);
      return;
    }

    // Update UI to show retry is in progress
    this.updateStepForRetry(index, step);

    // Report the error to the backend
    this.logger.info('📡 About to call error reporting API', {
      projectId: this.projectId,
      jobId: this.jobId,
      errorMessage: errorMessage.substring(0, 100) + '...',
      endpoint: '/error/build',
      apiCall: `POST /error/build?projectid=${this.projectId}&status_id=${this.jobId}`,
      payloadPreview: { error: errorMessage.substring(0, 50) + '...' }
    });

    this.errorReportingService.reportBuildError(this.projectId, this.jobId, errorMessage)
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        finalize(() => {
          this.logger.info('🔄 Error reporting completed, proceeding with retry');
          // Always proceed with retry regardless of error reporting success/failure
          this.proceedWithRetry(index);
        })
      )
      .subscribe({
        next: (response: any) => {
          this.logger.info('✅ BUILD/DEPLOY error reported successfully before retry', {
            projectId: this.projectId,
            jobId: this.jobId,
            progress: this.progress,
            response
          });
        },
        error: (error: any) => {
          this.logger.error('❌ Failed to report BUILD/DEPLOY error, but continuing with retry', {
            projectId: this.projectId,
            jobId: this.jobId,
            progress: this.progress,
            error
          });
          // Error reporting failed, but we still continue with the retry
          // The error is already handled by the ErrorReportingService
        }
      });
  }

  /**
   * Handle regular retry (non-BUILD failures)
   * @param index The step index
   * @param step The step being retried
   */
  private handleRegularRetry(index: number, step: StepperItem): void {
    this.logger.info('Handling regular retry', {
      stepTitle: step.title,
      retryCount: step.retryCount
    });

    // Update UI to show retry is in progress
    this.updateStepForRetry(index, step);

    // Proceed with retry immediately
    this.proceedWithRetry(index);
  }

  /**
   * Update step UI for retry state
   * @param index The step index
   * @param step The step being retried
   */
  private updateStepForRetry(index: number, step: StepperItem): void {
    // Change status back to IN_PROGRESS to indicate retry is happening
    this.status = 'in-progress';

    // Update the step's properties to show it's being retried
    step.completed = false;
    step.active = true;
    step.isRetrying = true; // Set the isRetrying flag for shimmer effect

    // Expand the step if it's collapsed
    this.collapsedSteps.delete(index);

    // Trigger change detection for OnPush
    this.cdr.markForCheck();
  }

  /**
   * Proceed with the actual retry process
   * @param index The step index
   */
  private proceedWithRetry(index: number): void {
    // Emit retry event with step index after a short delay
    // This matches the behavior in error-page component
    setTimeout(() => {
      // Emit the retry event to the parent component
      this.retryStep.emit(index);

      // Reset the error state in the stepper
      this.resetErrorState();

      // If using API, restart polling from the current step
      if (this.useApi && this.projectId && this.jobId) {
        this.restartPollingFromCurrentStep();
      }
    }, 500);
  }

  /**
   * Restart the exact same polling that was originally running
   * This should be identical to the original polling without any extra parameters
   */
  private restartPollingFromCurrentStep(): void {
    this.logger.info('Restarting original polling after retry', {
      projectId: this.projectId,
      jobId: this.jobId,
      note: 'Restarting exact same polling as original - no extra parameters'
    });

    // Stop current polling
    this.stopApiPolling();

    // Start the exact same polling as originally started using the same method
    this.startApiPolling();
  }

  /**
   * Resets the error state in the stepper
   * This is called when a retry is initiated
   */
  private resetErrorState(): void {
    // Change status to in-progress
    this.status = 'in-progress';

    // Reset any error-related properties
    // Find the step that is currently being retried
    const retryingStepIndex = this.steps.findIndex(step => step.isRetrying);
    if (retryingStepIndex >= 0) {
      // Keep the shimmer effect for a short time to provide visual feedback
      setTimeout(() => {
        // Reset the isRetrying flag after a delay
        this.steps[retryingStepIndex].isRetrying = false;
        // Trigger change detection for OnPush
        this.cdr.markForCheck();
      }, 3000); // Keep shimmer for 3 seconds
    }

    // Trigger change detection for OnPush
    this.cdr.markForCheck();
  }

  /**
   * Checks if a step has reached the maximum retry attempts
   * @param index The index of the step to check
   * @returns True if the step has reached the maximum retry attempts
   */
  hasReachedMaxRetries(index: number): boolean {
    if (index < 0 || index >= this.steps.length) {
      return false;
    }
    const step = this.steps[index];
    // If retryCount is undefined, treat it as 0
    const retryCount = step.retryCount || 0;
    return retryCount >= 3;
  }

  /**
   * Debug method to check if the retry button should be visible
   * @param step The step to check
   * @param index The index of the step
   * @returns True if the retry button should be visible
   */
  shouldShowRetryButton(step: StepperItem, index: number): boolean {
    const isFailure = this.isFailureStep(step);
    const notMaxRetries = !this.hasReachedMaxRetries(index);
    return isFailure && notMaxRetries;
  }

  /**
   * Debug method to create a failed step for testing
   * This method can be called from the parent component to test the retry button
   */
  public createFailedStepForTesting(): void {
    // Set status to FAILED
    this.status = 'FAILED';

    // Create a failed step
    const failedStep: StepperItem = {
      title: 'Test Failed',
      description: 'This is a test failed step for testing the retry button.',
      visibleTitle: 'Test Failed',
      visibleDescription: 'This is a test failed step for testing the retry button.',
      completed: false,
      active: true,
      isTitleTyping: false,
      isTyping: false,
      retryCount: 0,
      startTime: Date.now(),
      elapsedTime: 0,
      timerActive: false
    };

    // Add the failed step
    this.steps.push(failedStep);
    this.currentStep = failedStep;
    this.currentStepIndex = this.steps.length - 1;

    // Expand only the failed step
    this.collapsedSteps.delete(this.currentStepIndex);

    // Trigger change detection for OnPush
    this.cdr.markForCheck();
  }

  /**
   * Checks if a step is currently in processing state
   * @param index The index of the step to check
   * @returns True if the step is in processing state
   */
  isProcessingStep(index: number): boolean {
    // A step is processing if:
    // 1. The status is IN_PROGRESS
    // 2. This step is the current step
    // 3. The current step is not completed
    // 4. The step is not a failure step

    // First check if we have a valid current step
    if (!this.currentStep || index < 0 || index >= this.steps.length) {
      return false;
    }

    return (
      (this.status === 'IN_PROGRESS' || this.status === 'in-progress') &&
      index === this.currentStepIndex &&
      !this.currentStep.completed &&
      !this.isFailureStep(this.steps[index])
    );
  }

  /**
   * Checks if a step is a processed step (completed but not the current processing step)
   * @param index The index of the step to check
   * @returns True if the step is a processed step
   */
  isProcessedStep(index: number): boolean {
    // A step is processed if:
    // 1. It has a valid index
    // 2. It is completed
    // 3. It is not the current processing step

    if (index < 0 || index >= this.steps.length) {
      return false;
    }

    const step = this.steps[index];
    return step.completed && !this.isProcessingStep(index);
  }

  /**
   * Helper method to add a final step (completed or build succeeded)
   * @param step The step to add
   */
  private addFinalStep(step: StepperItem): void {
    // Mark previous step as completed
    const prevStepIndex = this.steps.length - 1;
    this.steps[prevStepIndex].completed = true;
    this.steps[prevStepIndex].active = false;
    // Stop the timer for the previous step and capture completion time
    this.stopStepTimer(prevStepIndex);

    // Smart collapse: Collapse the previous step when it completes,
    // but only if it's not user-expanded
    if (this.userExpandedStep !== prevStepIndex) {
      this.collapsedSteps.add(prevStepIndex);
    }

    // Smart collapse: Collapse other steps that are not user-expanded
    for (let i = 0; i < this.steps.length; i++) {
      if (i !== prevStepIndex && this.userExpandedStep !== i) {
        this.collapsedSteps.add(i);
      }
    }

    // Add the step
    this.steps.push(step);
    this.currentStep = step;
    this.currentStepIndex = this.steps.length - 1;

    // Ensure the final step is expanded
    this.collapsedSteps.delete(this.currentStepIndex);

    // Start the typewriter animation for the new step
    this.startTypewriterAnimation(this.currentStepIndex);

    this.stepUpdated.emit(this.currentStepIndex);

    // Ensure smooth scrolling to the new step
    setTimeout(() => {
      const stepElements = document.querySelectorAll('.stepper-item');
      if (stepElements && stepElements.length > 0) {
        const lastStep = stepElements[stepElements.length - 1] as HTMLElement;
        if (lastStep) {
          lastStep.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
      }
    }, 100);
  }

  /**
   * Extracts error message from a log field
   * @param logContent The log content to parse
   * @returns The extracted error message or empty string
   */
  private extractErrorMessage(logContent: string): string {
    if (!logContent) return '';

    try {
      // Check if the log content is a JSON string
      if (typeof logContent === 'string' && logContent.includes('{') && logContent.includes('}')) {
        // Try to parse the log content as JSON
        const parsedLog = JSON.parse(logContent);

        // If it has a message property, use that
        if (parsedLog.message) {
          return parsedLog.message;
        }

        // If it has an error property, use that
        if (parsedLog.error) {
          return parsedLog.error;
        }

        // If it has a data property with an error message, use that
        if (parsedLog.data && typeof parsedLog.data === 'string' &&
            (parsedLog.data.includes('error') || parsedLog.data.includes('Error') ||
             parsedLog.data.includes('failed') || parsedLog.data.includes('Failed'))) {
          return parsedLog.data;
        }
      }
    } catch (e) {
    }

    // If we couldn't extract an error message, return the original log content
    return logContent;
  }

  /**
   * Starts the typewriter animation for a step with progressive markdown rendering
   * @param stepIndex The index of the step to animate
   */
  private startTypewriterAnimation(stepIndex: number): void {
    if (stepIndex < 0 || stepIndex >= this.steps.length) {
      return;
    }

    const step = this.steps[stepIndex];

    // Clear any existing typing animations for this step
    if (this.timeoutRefs[`typing-title-${stepIndex}`]) {
      clearTimeout(this.timeoutRefs[`typing-title-${stepIndex}`]);
    }
    if (this.timeoutRefs[`typing-desc-${stepIndex}`]) {
      clearTimeout(this.timeoutRefs[`typing-desc-${stepIndex}`]);
    }

    // Reset visible text if needed
    if (!step.visibleTitle) {
      step.visibleTitle = '';
    }
    if (!step.visibleDescription) {
      step.visibleDescription = '';
    }

    // Start title typing first
    this.startTitleTypewriter(stepIndex);
  }

  /**
   * Starts typewriter animation for the step title
   * @param stepIndex The index of the step to animate
   */
  private startTitleTypewriter(stepIndex: number): void {
    const step = this.steps[stepIndex];

    if (!step.isTitleTyping || step.visibleTitle === step.title) {
      // Title is complete, start description
      this.startDescriptionTypewriter(stepIndex);
      return;
    }

    const typeTitleChar = () => {
      if (!step.isTitleTyping) {
        return;
      }

      const currentLength = step.visibleTitle.length;
      const fullTitle = step.title;

      if (currentLength < fullTitle.length) {
        const nextChar = fullTitle.charAt(currentLength);
        step.visibleTitle = fullTitle.substring(0, currentLength + 1);

        // Very fluid typing for titles
        let nextDelay = this.typingSpeed * 0.5;

        // Minimal pauses for natural flow
        if (['.', '!', '?'].includes(nextChar)) {
          nextDelay = this.typingSpeed * 1.2;
        } else if ([',', ';', ':'].includes(nextChar)) {
          nextDelay = this.typingSpeed * 1.1;
        } else if (nextChar === ' ') {
          nextDelay = this.typingSpeed * 0.8;
        }

        this.cdr.markForCheck();
        this.timeoutRefs[`typing-title-${stepIndex}`] = setTimeout(typeTitleChar, nextDelay);
      } else {
        // Title complete, start description with minimal pause for fluid flow
        step.isTitleTyping = false;
        this.cdr.markForCheck();
        setTimeout(() => {
          this.startDescriptionTypewriter(stepIndex);
        }, this.typingSpeed * 1.5);
      }
    };

    this.timeoutRefs[`typing-title-${stepIndex}`] = setTimeout(typeTitleChar, this.typingSpeed);
  }

  /**
   * Starts typewriter animation for the step description with markdown support
   * @param stepIndex The index of the step to animate
   */
  private startDescriptionTypewriter(stepIndex: number): void {
    const step = this.steps[stepIndex];

    if (!step.isTyping || step.visibleDescription === step.description) {
      return;
    }

    const typeDescChar = () => {
      if (!step.isTyping) {
        return;
      }

      const currentLength = step.visibleDescription.length;
      const fullText = step.description;

      if (currentLength < fullText.length) {
        const nextChar = fullText.charAt(currentLength);
        step.visibleDescription = fullText.substring(0, currentLength + 1);

        // Very fluid typing for descriptions
        let nextDelay = this.typingSpeed;

        // Minimal pauses for smooth flow
        if (['.', '!', '?'].includes(nextChar)) {
          nextDelay = this.typingSpeed * 1.3;
        } else if ([',', ';', ':'].includes(nextChar)) {
          nextDelay = this.typingSpeed * 1.1;
        } else if (nextChar === ' ') {
          nextDelay = this.typingSpeed * 0.7;
        }

        // Trigger change detection for progressive markdown rendering
        this.cdr.markForCheck();
        this.timeoutRefs[`typing-desc-${stepIndex}`] = setTimeout(typeDescChar, nextDelay);
      } else {
        // Description typing complete
        step.isTyping = false;
        this.cdr.markForCheck();
      }
    };

    this.timeoutRefs[`typing-desc-${stepIndex}`] = setTimeout(typeDescChar, this.typingSpeed);
  }

  /**
   * Start the timer for the current active step
   */
  private startTimer(): void {
    // Stop any existing timer
    this.stopTimer();

    // Start a new timer that updates every second
    this.timerInterval = setInterval(() => {
      this.updateTimers();
    }, this.timerUpdateInterval);
  }

  /**
   * Stop the timer
   */
  private stopTimer(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }

  /**
   * Update the elapsed time for all active timers
   */
  private updateTimers(): void {
    const currentTime = Date.now();
    let hasActiveTimers = false;

    this.steps.forEach(step => {
      if (step.timerActive && step.startTime) {
        step.elapsedTime = Math.floor((currentTime - step.startTime) / 1000);
        hasActiveTimers = true;
      }
    });

    // Stop the timer if no steps have active timers
    if (!hasActiveTimers) {
      this.stopTimer();
    }

    // Trigger change detection for OnPush
    this.cdr.markForCheck();
  }

  /**
   * Stop the timer for a specific step and capture completion time
   */
  private stopStepTimer(stepIndex: number): void {
    if (stepIndex >= 0 && stepIndex < this.steps.length) {
      const step = this.steps[stepIndex];
      if (step.timerActive && step.startTime) {
        // Capture the final completion time
        step.completionTime = Math.floor((Date.now() - step.startTime) / 1000);
        step.elapsedTime = step.completionTime; // Ensure elapsedTime matches completion time
      }
      step.timerActive = false;
    }
  }

  /**
   * Format elapsed time into a readable string (M:SS format like 0:06)
   */
  formatElapsedTime(elapsedTime: number): string {
    const minutes = Math.floor(elapsedTime / 60);
    const seconds = elapsedTime % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}

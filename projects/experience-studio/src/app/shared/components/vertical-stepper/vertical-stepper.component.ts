import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
  OnDestroy,
  Output,
  EventEmitter,
  ChangeDetectorRef,
  ChangeDetectionStrategy,
  inject,
  DestroyRef,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MarkdownModule } from 'ngx-markdown';
import { finalize } from 'rxjs/operators';
import { StepperState, StepperStateDisplayTitles } from '../../models/stepper-states.enum';
import { PollingService } from '../../services/polling.service';
import { ErrorReportingService } from '../../services/error-reporting.service';
import { createLogger } from '../../utils/logger';
import { ContentSanitizationService } from '../../services/content-sanitization.service';

export interface StepperItem {
  title: string;
  description: string;
  visibleTitle: string;
  visibleDescription: string;
  completed: boolean;
  active: boolean;
  collapsed?: boolean;
  isTyping?: boolean;
  isTitleTyping?: boolean;
  retryCount?: number;
  isRetrying?: boolean;
  startTime?: number;
  elapsedTime?: number;
  timerActive?: boolean;
  completionTime?: number;
}

@Component({
  selector: 'app-vertical-stepper',
  standalone: true,
  imports: [CommonModule, MarkdownModule],
  templateUrl: './vertical-stepper.component.html',
  styleUrls: ['./vertical-stepper.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VerticalStepperComponent implements OnChanges, OnInit, OnDestroy {
  @Input() progress: string = '';
  @Input() progressDescription: string = '';
  @Input() status: string = 'PENDING';
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() restartable: boolean = false;
  @Input() projectId: string = '';
  @Input() jobId: string = '';
  @Input() useApi: boolean = false;
  @Output() stepUpdated = new EventEmitter<number>();
  @Output() retryStep = new EventEmitter<number>();

  private logger = createLogger('VerticalStepperComponent');

  steps: StepperItem[] = [];
  currentStep: StepperItem | null = null;
  currentStepIndex: number = 0;
  animatingLine: boolean = false;
  private timeoutRefs: { [key: string]: any } = {};
  private typingSpeed: number = 15;
  private userExpandedStep: number | null = null;
  private timerInterval: any;
  private timerUpdateInterval: number = 1000;
  private collapsedSteps: Set<number> = new Set();
  private stepperStateMap = StepperStateDisplayTitles;
  public StepperState = StepperState;

  private readonly errorReportingService = inject(ErrorReportingService);
  private readonly destroyRef = inject(DestroyRef);

  constructor(
    private pollingService: PollingService,
    private cdr: ChangeDetectorRef,
    private contentSanitizationService: ContentSanitizationService
  ) {}

  ngOnInit(): void {
    if (this.useApi && this.projectId && this.jobId) {
      this.startApiPolling();
    } else if (this.steps.length === 0 && this.progress) {
      this.initializeFirstStep();
    }

    this.initializeExistingSteps();
  }

  private initializeFirstStep(): void {
    const step: StepperItem = {
      title: this.getDisplayTitleForProgress(this.progress),
      description: this.formatDescription(this.progressDescription) || 'Starting process...',
      visibleTitle: '',
      visibleDescription: '',
      completed: false,
      active: true,
      isTitleTyping: true,
      isTyping: true,
      startTime: Date.now(),
      elapsedTime: 0,
      timerActive: true,
    };

    this.steps.push(step);
    this.currentStep = this.steps[0];
    this.currentStepIndex = 0;

    if (this.status === 'IN_PROGRESS') {
      this.collapsedSteps.delete(this.currentStepIndex);
    }

    this.stepUpdated.emit(this.currentStepIndex);
    setTimeout(() => this.startTypewriterAnimation(this.currentStepIndex), 100);
    this.startTimer();
  }

  private initializeExistingSteps(): void {
    this.steps.forEach((step, i) => {
      if (!step.visibleDescription || !step.visibleTitle) {
        Object.assign(step, {
          visibleTitle: step.visibleTitle || '',
          visibleDescription: step.visibleDescription || '',
          isTitleTyping: true,
          isTyping: true,
        });
        setTimeout(() => this.startTypewriterAnimation(i), 100 * (i + 1));
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.useApi && (changes['projectId'] || changes['jobId'])) {
      if (this.projectId && this.jobId) {
        this.stopApiPolling();
        this.startApiPolling();
      }
    } else if (
      !this.useApi &&
      (changes['progress'] || changes['progressDescription'] || changes['status'])
    ) {
      this.updateStepper();
    }

    if (changes['progressDescription'] && this.status === 'FAILED') {
      this.updateFailedStepDescription();
    }
  }

  private updateFailedStepDescription(): void {
    const failedStepIndex = this.steps.findIndex(step => this.isFailureStep(step));
    if (failedStepIndex < 0) return;

    let errorMessage = '';
    if (this.progressDescription?.trim()) {
      errorMessage = this.formatDescription(this.progressDescription);
    } else if (this.useApi) {
      const lastResponse = this.pollingService.getLastStatusResponse();
      if (lastResponse?.details?.log) {
        errorMessage = this.extractErrorMessage(lastResponse.details.log);
      }
    }

    if (errorMessage) {
      const failedStep = this.steps[failedStepIndex];
      if (failedStep.description !== errorMessage) {
        Object.assign(failedStep, {
          description: errorMessage,
          visibleTitle: '',
          visibleDescription: '',
          isTitleTyping: true,
          isTyping: true,
        });
        this.startTypewriterAnimation(failedStepIndex);
        this.cdr.markForCheck();
      }
    }
  }

  ngOnDestroy(): void {
    this.clearAllTimeouts();
    this.stopTimer();
    this.stopApiPolling();
    this.userExpandedStep = null;
  }

  private startApiPolling(): void {
    if (!this.projectId || !this.jobId) return;

    this.pollingService.startPolling(this.projectId, this.jobId);

    this.pollingService.status$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((status: string) => {
        this.status = status;
        this.cdr.markForCheck();
      });

    this.pollingService.progress$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((progress: string) => {
        if (progress) {
          this.progress = progress;
          this.updateStepper();
          this.cdr.markForCheck();
        }
      });

    this.pollingService.progressDescription$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((description: string) => {
        if (description) {
          this.progressDescription = description;
          this.updateStepper();
          this.cdr.markForCheck();
        }
      });
  }

  private stopApiPolling(): void {
    this.pollingService.stopPolling();
  }

  private clearAllTimeouts(): void {
    Object.values(this.timeoutRefs).forEach(timeoutId => clearTimeout(timeoutId));
    this.timeoutRefs = {};
  }

  private cleanupUserExpandedTracking(): void {
    if (this.userExpandedStep !== null && this.userExpandedStep >= this.steps.length) {
      this.userExpandedStep = null;
    }
  }

  private formatDescription(description?: string): string {
    if (!description) return '';
    return this.contentSanitizationService.preprocessStepperDescription(description);
  }

  formatTitle(title: string): string {
    return title.replace(/_/g, ' ');
  }

  getSanitizedDescription(description: string): string {
    return this.contentSanitizationService.sanitizeForMarkdown(description);
  }

  getStepStatus(index: number): string {
    if (index < this.currentStepIndex) return 'completed';
    if (index === this.currentStepIndex) return 'active';
    if (index === this.currentStepIndex + 1) return 'next';
    return 'future';
  }

  getStepClasses(index: number): string[] {
    const classes = [this.getStepStatus(index)];
    if (this.isStepCollapsed(index)) classes.push('collapsed');
    if (this.status === 'IN_PROGRESS' && this.isProcessingStep(index))
      classes.push('in-progress-mode');
    return classes;
  }

  getCircleClasses(step: StepperItem, index: number): string[] {
    const classes = ['cursor-pointer'];
    const status = this.getStepStatus(index);
    const isFailure = this.isFailureStep(step);

    if (status === 'active' && !step.completed && !isFailure) classes.push('active');
    if (isFailure || (this.status === 'FAILED' && index === this.currentStepIndex))
      classes.push('failed');
    if (this.status === 'IN_PROGRESS' && this.isProcessingStep(index)) classes.push('processing');

    return classes;
  }

  isStepCompleted(index: number): boolean {
    return this.getStepStatus(index) === 'completed';
  }

  private isFailedBuildStep(step: StepperItem): boolean {
    return step.title === this.getDisplayTitleForProgress(StepperState.BUILD_FAILED);
  }

  showSuccessIcon(step: StepperItem, index: number): boolean {
    return (
      (this.isStepCompleted(index) || step.completed) &&
      !this.isFailureStep(step) &&
      !this.isFailedBuildStep(step)
    );
  }

  showErrorIcon(step: StepperItem, index: number): boolean {
    return (
      this.isFailureStep(step) ||
      (this.status === 'FAILED' && index === this.currentStepIndex) ||
      this.isFailedBuildStep(step)
    );
  }

  showLoadingSpinner(step: StepperItem, index: number): boolean {
    return (
      this.getStepStatus(index) === 'active' &&
      !step.completed &&
      this.status !== 'FAILED' &&
      !this.isFailureStep(step) &&
      !this.isFailedBuildStep(step)
    );
  }

  showStepNumber(step: StepperItem, index: number): boolean {
    const status = this.getStepStatus(index);
    return status !== 'completed' && status !== 'active' && !step.completed;
  }

  showTimer(step: StepperItem): boolean {
    return (
      (step.timerActive && step.elapsedTime !== undefined) || step.completionTime !== undefined
    );
  }

  getStepIconType(step: StepperItem, index: number): string {
    if (this.showSuccessIcon(step, index)) return 'success';
    if (this.showErrorIcon(step, index)) return 'error';
    if (this.showLoadingSpinner(step, index)) return 'loading';
    return 'number';
  }

  getStepTitle(step: StepperItem): string {
    return this.formatTitle(step.visibleTitle || step.title);
  }

  isTimerCompleted(step: StepperItem): boolean {
    return step.completionTime !== undefined && !step.timerActive;
  }

  getFormattedTime(step: StepperItem): string {
    return this.formatElapsedTime(step.completionTime || step.elapsedTime || 0);
  }

  getRetryTitle(step: StepperItem): string {
    return `Retry attempt ${step.retryCount || 0}/3`;
  }

  getDescriptionClasses(step: StepperItem, index: number): string[] {
    const classes = [];
    if (this.isStepCollapsed(index)) classes.push('collapsed');
    if (step.isTyping) classes.push('typing');
    if (step.isRetrying) classes.push('shimmer');
    return classes;
  }

  getStepDescription(step: StepperItem): string {
    return this.getSanitizedDescription(step.visibleDescription || step.description);
  }

  shouldShowStep(index: number): boolean {
    return index <= this.currentStepIndex || index === this.currentStepIndex + 1;
  }

  isLineAnimating(index: number): boolean {
    return this.animatingLine && index === this.currentStepIndex - 1;
  }

  isStepCollapsed(index: number): boolean {
    return this.collapsedSteps.has(index);
  }

  private isFinalDeploymentStage(): boolean {
    const progressUpper = this.progress.toUpperCase();
    return progressUpper === 'DEPLOYED' || progressUpper === 'DEPLOY';
  }

  toggleStepCollapse(index: number): void {
    const isCurrentlyCollapsed = this.collapsedSteps.has(index);
    const isProcessingStep = this.status === 'IN_PROGRESS' && index === this.currentStepIndex;

    if (isCurrentlyCollapsed) {
      for (let i = 0; i < this.steps.length; i++) {
        if (i !== index && !(this.status === 'IN_PROGRESS' && i === this.currentStepIndex)) {
          this.collapsedSteps.add(i);
        }
      }

      this.collapsedSteps.delete(index);

      if (!isProcessingStep) {
        this.userExpandedStep = index;
      }

      const step = this.steps[index];
      if (
        step &&
        step.description &&
        (!step.visibleDescription || step.visibleDescription.length < step.description.length)
      ) {
        step.isTyping = true;
        this.startTypewriterAnimation(index);
      }
    } else {
      this.collapsedSteps.add(index);

      if (this.userExpandedStep === index) {
        this.userExpandedStep = null;
      }
    }

    this.cdr.markForCheck();
  }

  restartStepper(): void {
    this.clearAllTimeouts();
    this.steps = [];
    this.currentStepIndex = 0;
    this.currentStep = null;
    this.animatingLine = false;
    this.collapsedSteps.clear();

    if (this.useApi && this.projectId && this.jobId) {
      this.stopApiPolling();
      this.startApiPolling();
    } else if (this.progress) {
      const displayTitle = this.getDisplayTitleForProgress(this.progress);
      const description = this.formatDescription(this.progressDescription) || 'Starting process...';
      this.steps.push({
        title: displayTitle,
        description: description,
        visibleTitle: '',
        visibleDescription: '',
        completed: false,
        active: true,
        isTitleTyping: true,
        isTyping: true,
        startTime: Date.now(),
        elapsedTime: 0,
        timerActive: true,
      });
      this.currentStep = this.steps[0];

      const processingStepIndex =
        this.status === 'IN_PROGRESS' || this.status === 'in-progress' ? 0 : -1;
      for (let i = 0; i < this.steps.length; i++) {
        if (i !== processingStepIndex) {
          this.collapsedSteps.add(i);
        }
      }
      this.collapsedSteps.delete(0);

      this.stepUpdated.emit(0);
      this.startTypewriterAnimation(0);
      this.startTimer();
    }

    this.cdr.markForCheck();
  }

  public resetStepper(): void {
    this.clearAllTimeouts();
    this.steps = [];
    this.currentStepIndex = 0;
    this.currentStep = null;
    this.animatingLine = false;
    this.collapsedSteps.clear();
    this.progress = '';
    this.progressDescription = '';
    this.status = 'PENDING';

    if (this.useApi) {
      this.stopApiPolling();
    }

    this.stepUpdated.emit(-1);
    this.cdr.markForCheck();
  }

  private updateStepper(): void {
    if (!this.progress?.trim()) return;

    if (
      this.progress === StepperState.BUILD_FAILED ||
      this.status === 'FAILED' ||
      this.status === 'failed'
    ) {
      this.handleFailedStep(this.progress, this.progressDescription);
      return;
    }

    const displayTitle = this.getDisplayTitleForProgress(this.progress);
    const existingStepIndex = this.findExistingStep(displayTitle);

    if (existingStepIndex === -1) {
      this.addNewStep(displayTitle);
    } else {
      this.updateExistingStep(existingStepIndex, displayTitle);
    }

    this.handleStatusCompletion();
  }

  private findExistingStep(displayTitle: string): number {
    return this.steps.findIndex(
      step => step.title === displayTitle || step.title === this.progress
    );
  }

  private addNewStep(displayTitle: string): void {
    const newStep = this.createStepItem(displayTitle);

    if (this.steps.length > 0) {
      this.completePreviousStep();
    }

    this.animateLineConnection();
    this.steps.push(newStep);
    this.setCurrentStep(newStep, this.steps.length - 1);
    this.startStepAnimations();
    this.scrollToNewStep();
  }

  private createStepItem(title: string): StepperItem {
    return {
      title,
      description: this.formatDescription(this.progressDescription) || '',
      visibleTitle: '',
      visibleDescription: '',
      completed: false,
      active: true,
      isTitleTyping: true,
      isTyping: true,
      startTime: Date.now(),
      elapsedTime: 0,
      timerActive: true,
    };
  }

  private completePreviousStep(): void {
    const prevStepIndex = this.steps.length - 1;
    const prevStep = this.steps[prevStepIndex];

    Object.assign(prevStep, { completed: true, active: false });
    this.stopStepTimer(prevStepIndex);
    this.applySmartCollapse(prevStepIndex);
  }

  private applySmartCollapse(excludeIndex?: number): void {
    this.steps.forEach((_, i) => {
      if (i !== excludeIndex && this.userExpandedStep !== i && i !== this.currentStepIndex) {
        this.collapsedSteps.add(i);
      }
    });
  }

  private animateLineConnection(): void {
    this.animatingLine = true;
    this.timeoutRefs['line-animation'] = setTimeout(() => (this.animatingLine = false), 2500);
  }

  private setCurrentStep(step: StepperItem, index: number): void {
    this.currentStep = step;
    this.currentStepIndex = index;
    this.collapsedSteps.delete(index);
    this.stepUpdated.emit(index);
  }

  private startStepAnimations(): void {
    this.startTypewriterAnimation(this.currentStepIndex);
    if (!this.timerInterval) this.startTimer();
  }

  private scrollToNewStep(): void {
    setTimeout(() => {
      const lastStep = document.querySelector('.stepper-item:last-child') as HTMLElement;
      lastStep?.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }, 100);
  }

  private updateExistingStep(index: number, displayTitle: string): void {
    const step = this.steps[index];

    if (this.shouldUpdateDescription(step)) {
      this.updateStepDescription(step, index);
    }

    step.title = displayTitle;
    this.setCurrentStep(step, index);
    this.updateStepStates(index);
    this.applySmartCollapse();
  }

  private shouldUpdateDescription(step: StepperItem): boolean {
    if (!this.progressDescription?.trim()) return false;
    const newDescription = this.formatDescription(this.progressDescription);
    return step.description !== newDescription;
  }

  private updateStepDescription(step: StepperItem, index: number): void {
    step.description = this.formatDescription(this.progressDescription);
    step.visibleTitle = '';
    step.visibleDescription = '';
    step.isTitleTyping = true;
    step.isTyping = true;
    this.startTypewriterAnimation(index);
  }

  private updateStepStates(activeIndex: number): void {
    this.steps.forEach((step, i) => {
      step.active = i === activeIndex;
      step.completed = i < activeIndex;
    });
  }

  private handleStatusCompletion(): void {
    if (
      (this.status === 'COMPLETED' || this.status === 'completed') &&
      this.isFinalDeploymentStage()
    ) {
      this.handleFinalCompletion();
    } else if (
      (this.status === 'COMPLETED' || this.status === 'completed') &&
      !this.isFinalDeploymentStage()
    ) {
      this.handleStepCompletion();
    } else if (this.status === 'FAILED' || this.status === 'failed') {
      this.handleFailedStep(this.progress, this.progressDescription);
    }
    this.cleanupUserExpandedTracking();
  }

  private handleFinalCompletion(): void {
    this.steps.forEach(step => this.completeStep(step));
    if (this.progress === StepperState.BUILD_SUCCEEDED) {
      this.handleBuildSucceeded();
    }
  }

  private handleStepCompletion(): void {
    if (this.currentStep) this.completeStep(this.currentStep);
  }

  private completeStep(step: StepperItem): void {
    Object.assign(step, { completed: true, active: false });
    if (step.timerActive && step.startTime) {
      const completionTime = Math.floor((Date.now() - step.startTime) / 1000);
      Object.assign(step, { completionTime, elapsedTime: completionTime, timerActive: false });
    }
  }

  private handleBuildSucceeded(): void {
    const buildSucceededStepIndex = this.steps.findIndex(
      step => step.title === this.getDisplayTitleForProgress(StepperState.BUILD_SUCCEEDED)
    );

    if (buildSucceededStepIndex === -1 && this.steps.length > 0) {
      const buildSucceededStep = this.createBuildSucceededStep();
      this.addFinalStep(buildSucceededStep);
    } else if (buildSucceededStepIndex !== -1) {
      this.updateBuildSucceededStep(buildSucceededStepIndex);
    }
  }

  private createBuildSucceededStep(): StepperItem {
    return {
      title: this.getDisplayTitleForProgress(StepperState.BUILD_SUCCEEDED),
      description:
        this.formatDescription(this.progressDescription) || 'Build completed successfully.',
      visibleTitle: '',
      visibleDescription: '',
      completed: true,
      active: true,
      isTitleTyping: true,
      isTyping: true,
      startTime: Date.now(),
      elapsedTime: 0,
      timerActive: false,
    };
  }

  private updateBuildSucceededStep(index: number): void {
    const step = this.steps[index];
    step.completed = true;
    step.active = true;

    if (this.progressDescription?.trim()) {
      const newDescription = this.formatDescription(this.progressDescription);
      if (step.description !== newDescription) {
        step.description = newDescription;
        step.visibleTitle = '';
        step.visibleDescription = '';
        step.isTitleTyping = true;
        step.isTyping = true;
        this.startTypewriterAnimation(index);
      }
    }

    this.setCurrentStep(step, index);
    this.applySmartCollapse();
    this.stepUpdated.emit(index);
  }

  private handleFailedStep(progress: string, progressDescription: string): void {
    let failedStepTitle = '';
    let defaultFailureMessage = 'Process failed.';

    if (progress === StepperState.BUILD_STARTED || progress === StepperState.BUILD_FAILED) {
      failedStepTitle = this.getDisplayTitleForProgress(StepperState.BUILD_FAILED);
      defaultFailureMessage = 'Build process failed.';
    } else {
      const currentStepTitle = this.getDisplayTitleForProgress(progress);
      failedStepTitle = `${currentStepTitle} Failed`;
    }

    let failedStepIndex = -1;

    if (progress !== StepperState.BUILD_FAILED) {
      failedStepIndex = this.steps.findIndex(
        step => step.title === failedStepTitle || this.isFailureStep(step)
      );
    }

    this.status = 'FAILED';

    let errorMessage = '';

    if (progressDescription && progressDescription.trim() !== '') {
      errorMessage = this.formatDescription(progressDescription);
    }

    if (!errorMessage && this.useApi) {
      const lastResponse = this.pollingService.getLastStatusResponse();
      if (lastResponse && lastResponse.details && lastResponse.details.log) {
        const logContent = lastResponse.details.log;
        errorMessage = this.extractErrorMessage(logContent);
      }
    }

    const formattedDescription = errorMessage || defaultFailureMessage;

    if (failedStepIndex === -1) {
      const failedStep: StepperItem = {
        title: failedStepTitle,
        description: formattedDescription,
        visibleTitle: '',
        visibleDescription: '',
        completed: false,
        active: true,
        isTitleTyping: true,
        isTyping: true,
        retryCount: 0,
        startTime: Date.now(),
        elapsedTime: 0,
        timerActive: false,
      };

      if (this.currentStep) {
        this.currentStep.active = false;
        this.currentStep.completed = false;

        for (let i = 0; i < this.steps.length; i++) {
          if (i === this.currentStepIndex || (this.steps[i].active && !this.steps[i].completed)) {
            this.steps[i].completed = false;
            this.steps[i].active = false;
          }
        }
      }

      for (let i = 0; i < this.steps.length; i++) {
        if (this.userExpandedStep !== i) {
          this.collapsedSteps.add(i);
        }
      }

      this.steps.push(failedStep);
      this.currentStep = failedStep;
      this.currentStepIndex = this.steps.length - 1;

      this.collapsedSteps.delete(this.currentStepIndex);
      this.startTypewriterAnimation(this.currentStepIndex);
      this.stepUpdated.emit(this.currentStepIndex);

      setTimeout(() => {
        const stepElements = document.querySelectorAll('.stepper-item');
        if (stepElements && stepElements.length > 0) {
          const lastStep = stepElements[stepElements.length - 1] as HTMLElement;
          if (lastStep) {
            lastStep.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
          }
        }
      }, 100);
    } else {
      const failedStep = this.steps[failedStepIndex];

      failedStep.description = formattedDescription;
      failedStep.visibleTitle = '';
      failedStep.visibleDescription = '';
      failedStep.isTitleTyping = true;
      failedStep.isTyping = true;

      failedStep.active = true;
      failedStep.completed = false;

      this.currentStep = failedStep;
      this.currentStepIndex = failedStepIndex;

      for (let i = 0; i < this.steps.length; i++) {
        if (i !== failedStepIndex && this.userExpandedStep !== i) {
          this.collapsedSteps.add(i);
        }
      }

      this.collapsedSteps.delete(failedStepIndex);
      this.startTypewriterAnimation(failedStepIndex);
      this.stepUpdated.emit(failedStepIndex);
    }
  }

  public getDisplayTitleForProgress(progress: string): string {
    if (Object.values(StepperState).includes(progress as StepperState)) {
      return this.stepperStateMap[progress as StepperState];
    }
    return progress;
  }

  isFailureStep(step: StepperItem): boolean {
    return (
      step.title.includes('Failed') ||
      step.title === this.getDisplayTitleForProgress(StepperState.BUILD_FAILED) ||
      ((this.status === 'FAILED' || this.status === 'failed') && step === this.currentStep)
    );
  }

  onRetryClick(index: number, event?: Event): void {
    if (event) {
      event.stopPropagation();
    }

    const step = this.steps[index];

    if (step.retryCount === undefined) {
      step.retryCount = 0;
    }

    step.retryCount++;

    this.logger.info('Retry button clicked', {
      stepIndex: index,
      stepTitle: step.title,
      retryCount: step.retryCount,
      progress: this.progress,
      status: this.status,
    });

    const isBuildOrDeployFailure =
      this.progress === StepperState.BUILD ||
      this.progress === StepperState.BUILD_FAILED ||
      this.progress === StepperState.DEPLOY ||
      this.progress === StepperState.DEPLOYED ||
      step.title.toLowerCase().includes('build') ||
      step.title.toLowerCase().includes('deploy');

    this.logger.info('🔍 Retry condition evaluation:', {
      isBuildOrDeployFailure,
      progress: this.progress,
      status: this.status,
      statusTrimmed: this.status?.trim().toUpperCase(),
      useApi: this.useApi,
      hasProjectId: !!this.projectId,
      hasJobId: !!this.jobId,
      projectId: this.projectId,
      jobId: this.jobId,
      stepTitle: step.title,
    });

    if (
      isBuildOrDeployFailure &&
      (this.status?.trim().toUpperCase() === 'FAILED' ||
        this.status?.trim().toLowerCase() === 'failed') &&
      this.useApi &&
      this.projectId &&
      this.jobId
    ) {
      this.logger.info('✅ Triggering BUILD/DEPLOY retry with error reporting');
      this.handleBuildRetryWithErrorReporting(index, step);
    } else {
      this.logger.warn('❌ Using regular retry instead of error reporting retry', {
        reason: !isBuildOrDeployFailure
          ? 'Not BUILD/DEPLOY failure'
          : this.status?.trim().toUpperCase() !== 'FAILED'
            ? `Status not FAILED (actual: '${this.status}')`
            : !this.useApi
              ? 'useApi is false'
              : !this.projectId
                ? 'No projectId'
                : !this.jobId
                  ? 'No jobId'
                  : 'Unknown reason',
      });
      this.handleRegularRetry(index, step);
    }
  }

  private handleBuildRetryWithErrorReporting(index: number, step: StepperItem): void {
    this.logger.info('🚀 Handling BUILD/DEPLOY retry with error reporting', {
      projectId: this.projectId,
      jobId: this.jobId,
      stepTitle: step.title,
      progress: this.progress,
      status: this.status,
      useApi: this.useApi,
      hasProjectId: !!this.projectId,
      hasJobId: !!this.jobId,
      projectIdLength: this.projectId?.length || 0,
      jobIdLength: this.jobId?.length || 0,
    });

    const errorMessage = this.pollingService.getErrorMessageFromLastResponse();
    const lastResponse = this.pollingService.getLastStatusResponse();

    this.logger.info('🔍 Error message extraction result:', {
      hasErrorMessage: !!errorMessage,
      errorMessageLength: errorMessage?.length || 0,
      errorMessagePreview: errorMessage?.substring(0, 100) || 'No message',
      hasLastResponse: !!lastResponse,
      lastResponseKeys: lastResponse ? Object.keys(lastResponse) : [],
      lastResponseDetails: lastResponse?.details ? Object.keys(lastResponse.details) : [],
      lastResponseLog: lastResponse?.details?.log ? 'Has log field' : 'No log field',
    });

    if (!errorMessage) {
      this.logger.warn(
        '❌ No error message found for BUILD/DEPLOY retry, proceeding without error reporting'
      );
      this.handleRegularRetry(index, step);
      return;
    }

    this.updateStepForRetry(index, step);

    this.logger.info('📡 About to call error reporting API', {
      projectId: this.projectId,
      jobId: this.jobId,
      errorMessage: errorMessage.substring(0, 100) + '...',
      endpoint: '/error/build',
      apiCall: `POST /error/build?projectid=${this.projectId}&status_id=${this.jobId}`,
      payloadPreview: { error: errorMessage.substring(0, 50) + '...' },
    });

    this.errorReportingService
      .reportBuildError(this.projectId, this.jobId, errorMessage)
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        finalize(() => {
          this.logger.info('🔄 Error reporting completed, proceeding with retry');

          this.proceedWithRetry(index);
        })
      )
      .subscribe({
        next: (response: any) => {
          this.logger.info('✅ BUILD/DEPLOY error reported successfully before retry', {
            projectId: this.projectId,
            jobId: this.jobId,
            progress: this.progress,
            response,
          });
        },
        error: (error: any) => {
          this.logger.error('❌ Failed to report BUILD/DEPLOY error, but continuing with retry', {
            projectId: this.projectId,
            jobId: this.jobId,
            progress: this.progress,
            error,
          });
        },
      });
  }

  private handleRegularRetry(index: number, step: StepperItem): void {
    this.logger.info('Handling regular retry', {
      stepTitle: step.title,
      retryCount: step.retryCount,
    });

    this.updateStepForRetry(index, step);
    this.proceedWithRetry(index);
  }

  private updateStepForRetry(index: number, step: StepperItem): void {
    this.status = 'in-progress';
    step.completed = false;
    step.active = true;
    step.isRetrying = true;
    this.collapsedSteps.delete(index);
    this.cdr.markForCheck();
  }

  private proceedWithRetry(index: number): void {
    setTimeout(() => {
      this.retryStep.emit(index);
      this.resetErrorState();

      if (this.useApi && this.projectId && this.jobId) {
        this.restartPollingFromCurrentStep();
      }
    }, 500);
  }

  private restartPollingFromCurrentStep(): void {
    this.logger.info('Restarting original polling after retry', {
      projectId: this.projectId,
      jobId: this.jobId,
      note: 'Restarting exact same polling as original - no extra parameters',
    });

    this.stopApiPolling();

    this.startApiPolling();
  }

  private resetErrorState(): void {
    this.status = 'in-progress';
    const retryingStepIndex = this.steps.findIndex(step => step.isRetrying);
    if (retryingStepIndex >= 0) {
      setTimeout(() => {
        this.steps[retryingStepIndex].isRetrying = false;

        this.cdr.markForCheck();
      }, 3000);
    }

    this.cdr.markForCheck();
  }

  hasReachedMaxRetries(index: number): boolean {
    if (index < 0 || index >= this.steps.length) {
      return false;
    }
    const step = this.steps[index];
    const retryCount = step.retryCount || 0;
    return retryCount >= 3;
  }

  shouldShowRetryButton(step: StepperItem, index: number): boolean {
    const isFailure = this.isFailureStep(step);
    const notMaxRetries = !this.hasReachedMaxRetries(index);
    return isFailure && notMaxRetries;
  }

  public createFailedStepForTesting(): void {
    this.status = 'FAILED';

    const failedStep: StepperItem = {
      title: 'Test Failed',
      description: 'This is a test failed step for testing the retry button.',
      visibleTitle: 'Test Failed',
      visibleDescription: 'This is a test failed step for testing the retry button.',
      completed: false,
      active: true,
      isTitleTyping: false,
      isTyping: false,
      retryCount: 0,
      startTime: Date.now(),
      elapsedTime: 0,
      timerActive: false,
    };

    this.steps.push(failedStep);
    this.currentStep = failedStep;
    this.currentStepIndex = this.steps.length - 1;
    this.collapsedSteps.delete(this.currentStepIndex);
    this.cdr.markForCheck();
  }

  isProcessingStep(index: number): boolean {
    if (!this.currentStep || index < 0 || index >= this.steps.length) {
      return false;
    }

    return (
      (this.status === 'IN_PROGRESS' || this.status === 'in-progress') &&
      index === this.currentStepIndex &&
      !this.currentStep.completed &&
      !this.isFailureStep(this.steps[index])
    );
  }

  isProcessedStep(index: number): boolean {
    if (index < 0 || index >= this.steps.length) {
      return false;
    }

    const step = this.steps[index];
    return step.completed && !this.isProcessingStep(index);
  }

  private addFinalStep(step: StepperItem): void {
    const prevStepIndex = this.steps.length - 1;
    this.steps[prevStepIndex].completed = true;
    this.steps[prevStepIndex].active = false;

    this.stopStepTimer(prevStepIndex);

    if (this.userExpandedStep !== prevStepIndex) {
      this.collapsedSteps.add(prevStepIndex);
    }

    for (let i = 0; i < this.steps.length; i++) {
      if (i !== prevStepIndex && this.userExpandedStep !== i) {
        this.collapsedSteps.add(i);
      }
    }

    this.steps.push(step);
    this.currentStep = step;
    this.currentStepIndex = this.steps.length - 1;

    this.collapsedSteps.delete(this.currentStepIndex);

    this.startTypewriterAnimation(this.currentStepIndex);

    this.stepUpdated.emit(this.currentStepIndex);

    setTimeout(() => {
      const stepElements = document.querySelectorAll('.stepper-item');
      if (stepElements && stepElements.length > 0) {
        const lastStep = stepElements[stepElements.length - 1] as HTMLElement;
        if (lastStep) {
          lastStep.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
      }
    }, 100);
  }

  private extractErrorMessage(logContent: string): string {
    if (!logContent) return '';

    try {
      if (typeof logContent === 'string' && logContent.includes('{') && logContent.includes('}')) {
        const parsedLog = JSON.parse(logContent);

        if (parsedLog.message) {
          return parsedLog.message;
        }

        if (parsedLog.error) {
          return parsedLog.error;
        }

        if (
          parsedLog.data &&
          typeof parsedLog.data === 'string' &&
          (parsedLog.data.includes('error') ||
            parsedLog.data.includes('Error') ||
            parsedLog.data.includes('failed') ||
            parsedLog.data.includes('Failed'))
        ) {
          return parsedLog.data;
        }
      }
    } catch (e) {}

    return logContent;
  }

  private startTypewriterAnimation(stepIndex: number): void {
    if (stepIndex < 0 || stepIndex >= this.steps.length) {
      return;
    }

    const step = this.steps[stepIndex];

    if (this.timeoutRefs[`typing-title-${stepIndex}`]) {
      clearTimeout(this.timeoutRefs[`typing-title-${stepIndex}`]);
    }
    if (this.timeoutRefs[`typing-desc-${stepIndex}`]) {
      clearTimeout(this.timeoutRefs[`typing-desc-${stepIndex}`]);
    }

    if (!step.visibleTitle) {
      step.visibleTitle = '';
    }
    if (!step.visibleDescription) {
      step.visibleDescription = '';
    }
    this.startTitleTypewriter(stepIndex);
  }

  private startTitleTypewriter(stepIndex: number): void {
    const step = this.steps[stepIndex];

    if (!step.isTitleTyping || step.visibleTitle === step.title) {
      this.startDescriptionTypewriter(stepIndex);
      return;
    }

    const typeTitleChar = () => {
      if (!step.isTitleTyping) {
        return;
      }

      const currentLength = step.visibleTitle.length;
      const fullTitle = step.title;

      if (currentLength < fullTitle.length) {
        const nextChar = fullTitle.charAt(currentLength);
        step.visibleTitle = fullTitle.substring(0, currentLength + 1);

        let nextDelay = this.typingSpeed * 0.5;

        if (['.', '!', '?'].includes(nextChar)) {
          nextDelay = this.typingSpeed * 1.2;
        } else if ([',', ';', ':'].includes(nextChar)) {
          nextDelay = this.typingSpeed * 1.1;
        } else if (nextChar === ' ') {
          nextDelay = this.typingSpeed * 0.8;
        }

        this.cdr.markForCheck();
        this.timeoutRefs[`typing-title-${stepIndex}`] = setTimeout(typeTitleChar, nextDelay);
      } else {
        step.isTitleTyping = false;
        this.cdr.markForCheck();
        setTimeout(() => {
          this.startDescriptionTypewriter(stepIndex);
        }, this.typingSpeed * 1.5);
      }
    };

    this.timeoutRefs[`typing-title-${stepIndex}`] = setTimeout(typeTitleChar, this.typingSpeed);
  }

  private startDescriptionTypewriter(stepIndex: number): void {
    const step = this.steps[stepIndex];

    if (!step.isTyping || step.visibleDescription === step.description) {
      return;
    }

    const typeDescChar = () => {
      if (!step.isTyping) {
        return;
      }

      const currentLength = step.visibleDescription.length;
      const fullText = step.description;

      if (currentLength < fullText.length) {
        const nextChar = fullText.charAt(currentLength);
        step.visibleDescription = fullText.substring(0, currentLength + 1);

        let nextDelay = this.typingSpeed;

        if (['.', '!', '?'].includes(nextChar)) {
          nextDelay = this.typingSpeed * 1.3;
        } else if ([',', ';', ':'].includes(nextChar)) {
          nextDelay = this.typingSpeed * 1.1;
        } else if (nextChar === ' ') {
          nextDelay = this.typingSpeed * 0.7;
        }

        this.cdr.markForCheck();
        this.timeoutRefs[`typing-desc-${stepIndex}`] = setTimeout(typeDescChar, nextDelay);
      } else {
        step.isTyping = false;
        this.cdr.markForCheck();
      }
    };

    this.timeoutRefs[`typing-desc-${stepIndex}`] = setTimeout(typeDescChar, this.typingSpeed);
  }

  private startTimer(): void {
    this.stopTimer();

    this.timerInterval = setInterval(() => {
      this.updateTimers();
    }, this.timerUpdateInterval);
  }

  private stopTimer(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }
  private updateTimers(): void {
    const currentTime = Date.now();
    let hasActiveTimers = false;

    this.steps.forEach(step => {
      if (step.timerActive && step.startTime) {
        step.elapsedTime = Math.floor((currentTime - step.startTime) / 1000);
        hasActiveTimers = true;
      }
    });

    if (!hasActiveTimers) {
      this.stopTimer();
    }

    this.cdr.markForCheck();
  }

  private stopStepTimer(stepIndex: number): void {
    if (stepIndex >= 0 && stepIndex < this.steps.length) {
      const step = this.steps[stepIndex];
      if (step.timerActive && step.startTime) {
        step.completionTime = Math.floor((Date.now() - step.startTime) / 1000);
        step.elapsedTime = step.completionTime;
      }
      step.timerActive = false;
    }
  }

  formatElapsedTime(elapsedTime: number): string {
    const minutes = Math.floor(elapsedTime / 60);
    const seconds = elapsedTime % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}

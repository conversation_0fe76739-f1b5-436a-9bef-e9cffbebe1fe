import {
  Component,
  OnInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  inject
} from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { HeroSectionHeaderComponent } from '../hero-section-header/hero-section-header.component';
import { RecentCreationComponent } from '../recent-creation/recent-creation.component';
import { CardDataService } from '../../services/data-services/card-data.service';
import { ThemeService } from '../../services/theme-service/theme.service';
import { ToastService } from '../../services/toast.service';
import { CardSelectionService } from '../../services/card-selection.service';
import { SubscriptionManager } from '../../utils/subscription-management.util';

interface StudioCard {
  readonly id: number;
  readonly title: string;
  readonly description: string;
  readonly image: string;
  readonly path: string;
  readonly type: string;
  readonly disabled?: boolean;
}

type Theme = 'light' | 'dark';

const STUDIO_CARDS: readonly StudioCard[] = [
  {
    id: 1,
    title: 'Generate UI Design',
    description: 'Create and generate UI designs with AI. Transform your ideas into beautiful interfaces.',
    image: '/assets/cards-images/ui_design.svg',
    path: 'prompt',
    type: 'generate-ui-design',
    disabled: false
  },
  {
    id: 2,
    title: 'Generate Application',
    description: 'Create complete applications with AI. From concept to functional code in minutes.',
    image: '/assets/cards-images/app_generation.svg',
    path: 'prompt',
    type: 'image-to-code'
  }
] as const;

@Component({
  selector: 'app-landing-page',
  standalone: true,
  imports: [
    CommonModule,
    HeroSectionHeaderComponent,
    RecentCreationComponent,
  ],
  templateUrl: './landing-page.component.html',
  styleUrl: './landing-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LandingPageComponent implements OnInit {
  private readonly router = inject(Router);
  private readonly cardDataService = inject(CardDataService);
  private readonly themeService = inject(ThemeService);
  private readonly toastService = inject(ToastService);
  private readonly cardSelectionService = inject(CardSelectionService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly subscriptionManager = new SubscriptionManager();

  theme: Theme = 'light';
  readonly studioCards = STUDIO_CARDS;

  // Pre-computed styles to avoid template calculations during theme switching
  cardBackground = 'transparent';
  cardTextColor = '#1D1D1D';
  cardDescriptionColor = '#595959';
  cardBorderColor = 'rgba(0, 0, 0, 0.1)';

  readonly trackByCardId = (_: number, card: StudioCard): number => card.id;

  // Route mappings for O(1) lookup performance
  private readonly routeMap = new Map<string, string>([
    ['Generate UI Design', '/experience/generate-ui-design'],
    ['Generate Application', '/experience/generate-application']
  ]);

  private readonly actionMap = new Map<string, string>([
    ['Generate UI Design', 'UI'],
    ['Generate Application', 'Application']
  ]);

  ngOnInit(): void {
    this.initTheme();
    this.cardSelectionService.resetSelectionState();
  }

  private initTheme(): void {
    this.theme = this.themeService.getCurrentTheme();
    this.updateThemeStyles();

    this.subscriptionManager.subscribe(
      this.themeService.themeObservable,
      (theme: Theme) => {
        this.theme = theme;
        this.updateThemeStyles();
        this.cdr.markForCheck();
      }
    );
  }

  private updateThemeStyles(): void {
    this.cardTextColor = this.theme === 'dark' ? '#ffffff' : '#1D1D1D';
    this.cardDescriptionColor = this.theme === 'dark' ? '#cccccc' : '#595959';
    this.cardBorderColor = this.theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
  }

  navigateToStudio(card: StudioCard, event: Event): void {
    event.stopPropagation();

    if (card.disabled) {
      this.toastService.info('This feature is in build mode.');
      return;
    }

    this.cardDataService.setSelectedCardTitle(card.title);
    this.cardSelectionService.setCardSelected(true);

    const routePrefix = this.getRoutePrefix(card.title);
    const actionType = this.getActionType(card.title);

    this.toastService.info(`Starting ${actionType} generation`);
    this.router.navigate([`${routePrefix}/prompt`]);
  }

  private getRoutePrefix(title: string): string {
    return this.routeMap.get(title) || '/experience/generate-application';
  }

  private getActionType(title: string): string {
    return this.actionMap.get(title) || 'Application';
  }
}

<main class="container-fluid" id="main-content-container">
  <section class="container-fluid d-flex justify-content-center">
    <app-hero-section-header
      [headerTitle]="'Dream Build Launch!'"
      [headerDescription]="'<PERSON><PERSON><PERSON><PERSON> blends Art Science to bring your ideas to life.'"
      [subHeading]="'What would you like to build today?'"></app-hero-section-header>
  </section>
  <section class="container-fluid d-flex justify-content-center">
    <div class="studio-cards-grid mx-auto p-3 gap-4 max-w-1000">
      <article
        class="studio-card rounded-2xl overflow-hidden cursor-pointer position-relative shadow-card h-280"
        *ngFor="let card of studioCards; trackBy: trackByCardId"
        (click)="navigateToStudio(card, $event)"
        [class.card-disabled]="card.disabled"
        [style.background-color]="cardBackground"
        [style.border-color]="cardBorderColor"
        [style.border-width]="'0.5px'"
        [style.border-style]="'solid'"
        [attr.aria-disabled]="card.disabled"
        role="button"
        tabindex="0"
        [attr.aria-label]="card.title + ' - ' + card.description">
        <div class="d-flex justify-content-between p-4 h-100">
          <div class="d-flex flex-column justify-content-between pe-4 flex-fill">
            <h2
              class="font-mulish font-weight-700 line-height-1-2 text-28 mb-3"
              [style.color]="cardTextColor">
              {{ card.title }}
            </h2>
            <p
              class="font-mulish font-weight-500 line-height-1-5 text-16 mb-4"
              [style.color]="cardDescriptionColor">
              {{ card.description }}
            </p>
          </div>
          <div class="d-flex align-items-center justify-content-center flex-fill">
            <img
              [src]="card.image"
              [alt]="card.title + ' illustration'"
              loading="lazy"
              decoding="async"
              fetchpriority="high" />
          </div>
        </div>
      </article>
    </div>
  </section>
  <section class="mt-5">
    <div class="d-flex align-items-center justify-content-center">
      <img
        src="/assets/icons/divider-light.svg"
        class="w-80 max-w-800 h-auto"
        tabindex="0"
        alt="divider"
        loading="lazy"
        decoding="async" />
    </div>
  </section>

  <section class="mt-4 py-5">
    <app-recent-creation></app-recent-creation>
  </section>
</main>

import { Component, ChangeDetectionStrategy, inject, DestroyRef, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject } from 'rxjs';
import { createLogger } from '../../../../utils/logger';
import { InfoIconComponent } from './info-icon.component';

interface ShortcutItem {
  label: string;
  keys: string[];
  description?: string;
}

interface ShortcutGroup {
  title: string;
  icon: string;
  shortcuts: ShortcutItem[];
}

@Component({
  selector: 'app-canvas-info',
  standalone: true,
  imports: [CommonModule, InfoIconComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <!-- Info Ball Toggle Button -->
    <div class="info-ball-container">
      <button
        class="info-ball"
        (click)="togglePanel()"
        [class.active]="isPanelVisible$ | async"
        [attr.aria-expanded]="isPanelVisible$ | async"
        [attr.aria-label]="(isPanelVisible$ | async) ? 'Hide canvas shortcuts' : 'Show canvas shortcuts'"
        title="Canvas Shortcuts">
        <app-info-icon
          [size]="20"
          [isActive]="isIconActive"
          ariaLabel="Canvas shortcuts info icon">
        </app-info-icon>
      </button>
    </div>

    <!-- Enhanced Shortcuts Panel -->
    <div
      class="canvas-shortcuts-panel"
      [class.visible]="isPanelVisible$ | async"
      [attr.aria-hidden]="!(isPanelVisible$ | async)"
      role="dialog"
      aria-labelledby="shortcuts-title">

      <div class="panel-header">
        <h4 id="shortcuts-title">
          <i class="bi bi-keyboard"></i>
          Canvas Shortcuts
        </h4>
        <button
          class="close-btn"
          (click)="hidePanel()"
          aria-label="Close shortcuts panel"
          title="Close">
          <i class="bi bi-x"></i>
        </button>
      </div>

      <div class="panel-content">
        <div class="shortcuts-section" *ngFor="let group of shortcutGroups">
          <div class="section-header">
            <h5>
              <i class="bi" [class]="group.icon"></i>
              {{ group.title }}
            </h5>
          </div>
          <div class="shortcuts-grid">
            <div class="shortcut-item" *ngFor="let shortcut of group.shortcuts">
              <span class="shortcut-label">{{ shortcut.label }}:</span>
              <div class="shortcut-keys">
                <ng-container *ngFor="let key of shortcut.keys; let last = last">
                  <kbd class="key">{{ key }}</kbd>
                  <span *ngIf="!last" class="key-separator">+</span>
                </ng-container>
              </div>
              <span *ngIf="shortcut.description" class="shortcut-description">
                {{ shortcut.description }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./canvas-info.component.scss']
})
export class CanvasInfoComponent {
  private readonly logger = createLogger('CanvasInfoComponent');
  private readonly destroyRef = inject(DestroyRef);
  private readonly cdr = inject(ChangeDetectorRef);

  // Panel visibility state management with BehaviorSubject
  private readonly isPanelVisibleSubject = new BehaviorSubject<boolean>(false);
  public readonly isPanelVisible$ = this.isPanelVisibleSubject.asObservable();

  // Shortcut groups data
  public readonly shortcutGroups: ShortcutGroup[] = [
    {
      title: 'Canvas Navigation',
      icon: 'bi-arrows-move',
      shortcuts: [
        { label: 'Pan Canvas', keys: ['Click', 'Drag'] },
        { label: 'Zoom In/Out', keys: ['Mouse Wheel'] },
        { label: 'Precise Zoom', keys: ['Ctrl', 'Scroll'] },
        { label: 'Reset View', keys: ['Reset Button'] },
        { label: 'Fit to View', keys: ['Fit Button'] }
      ]
    },
    {
      title: 'Node Selection',
      icon: 'bi-cursor-fill',
      shortcuts: [
        { label: 'Select Node', keys: ['Click'], description: 'Single click to select a page' },
        { label: 'Multi-Select', keys: ['Ctrl', 'Click'], description: 'Hold Ctrl/Cmd and click multiple nodes' },
        { label: 'Select All', keys: ['Select All Button'], description: 'Select all available pages' },
        { label: 'Clear Selection', keys: ['Clear Button'], description: 'Deselect all pages' }
      ]
    },
    {
      title: 'Node Interactions',
      icon: 'bi-square',
      shortcuts: [
        { label: 'Expand Node', keys: ['Double-Click'], description: 'Open page in full-screen modal' },
        { label: 'Edit Selected', keys: ['E'], description: 'Start editing selected pages' },
        { label: 'Edit Mode', keys: ['F2'], description: 'Alternative edit shortcut' }
      ]
    },
    {
      title: 'Keyboard Shortcuts',
      icon: 'bi-keyboard',
      shortcuts: [
        { label: 'Close Modal', keys: ['Esc'], description: 'Close any open modal or panel' }
      ]
    }
  ];

  constructor() {
    this.logger.info('🎯 Canvas Info Component initialized with enhanced shortcuts');
  }

  /**
   * Toggle panel visibility
   */
  togglePanel(): void {
    const currentState = this.isPanelVisibleSubject.value;
    this.isPanelVisibleSubject.next(!currentState);
    this.logger.info('📖 Canvas shortcuts panel toggled:', { visible: !currentState });
  }

  /**
   * Show the panel
   */
  showPanel(): void {
    this.isPanelVisibleSubject.next(true);
    this.logger.info('📖 Canvas shortcuts panel shown');
  }

  /**
   * Hide the panel
   */
  hidePanel(): void {
    this.isPanelVisibleSubject.next(false);
    this.logger.info('📖 Canvas shortcuts panel hidden');
  }

  /**
   * Get current panel visibility state
   */
  isPanelVisible(): boolean {
    return this.isPanelVisibleSubject.value;
  }

  /**
   * Get icon active state for template binding
   */
  get isIconActive(): boolean {
    return this.isPanelVisibleSubject.value;
  }
}

:root {
  // Light Theme Variables
  min-height: 100vh;
  width: 100%;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-image: url(../svgs/main-bg-light.svg);
  --background-color: #fff;
  --body-text-color: #000000;
  --main-title: linear-gradient(90deg, #6566cd 24%, #f63b8f 68%);
  --main-subtitle: #666d99;

  --card-border-color: #292c3d;
  --card-hover-bg: #f5f5f5;
  --selected-card-border: #3f51b5;
  --selected-card-bg: #e8eaf6;
  --button-bg: #cccccc;
  --button-hover-bg: #b3b3b3;
  --summary-bg: #f9f9f9;
  --summary-border: #dddddd;
  --option-card-background-color: #fafafa;

  --header-icon: #000000;
  --header-icon-border: #c2c5d6;
  --button-bg-color: #ffffff;
  --button-text-color: #292c3d;
  --button-border-color: #fff;

  //main-component hero-section and prompt-bar
  --hero-section-color: #14161f;
  --hero-section-heading-color: #dadce7;
  --prompt-bar-background-color: #000;
  --prompt-bar-border-color: #f96cab;
  --prompt-bar-hover-color: #6566cd;
  --prompt-bar-suggestion-button-bg: linear-gradient(
    102deg,
    rgba(240, 240, 245, 0.72) 2.05%,
    rgba(240, 240, 245, 0.72) 100%
  );

  // feature-card-main-page
  --feature-card-border-back: #e30a6d;
  --feature-card-btn-text-color: #ffffff;
  --feature-card-bg: rgb(250, 247, 247);
  --feature-card-border-front: 1.5px solid #fff;
  --loading-text-color: #14161f;
  --feature-card-text-color: #14161f;
  --feature-card-color: #070707;
  --feature-card-btn: linear-gradient(
    90deg,
    var(--Blue-B-500, #6566cd) 0%,
    var(--Pink-P-500, #e30a6d) 100%
  );

  // form-factor-component light theme
  --form-factor-btn-border: #29292c;
  --form-factor-btn-bg: none;
  --form-factor-card-hover: linear-gradient(90deg, #6566cd 24%, #f63b8f 68%);
  --form-factor-btn-text-color: #29292c;
  --right-panel-header-bg: #f0f0f580;

  // Code Viewer Variables - Light Theme
  --code-viewer-bg: rgba(240, 240, 245, 0.5);
  --code-viewer-text: #000000;
  --code-viewer-border: #e0e0e0;
  --code-viewer-file-hover: rgba(0, 0, 0, 0.05);
  --code-viewer-folder-hover: rgba(0, 0, 0, 0.05);
  --code-viewer-search-bg: transparent;
  --code-viewer-search-border: #e0e0e0;
  --code-viewer-search-focus: #333333;
  --code-viewer-search-placeholder: #666666;
  --code-viewer-arrow: #000000;
  --code-viewer-tab-bg: #f5f5f5;
  --code-viewer-tab-hover: rgba(0, 0, 0, 0.05);
  --code-viewer-tab-text: #000000;
  --code-viewer-monaco-bg: transparent;
  --code-viewer-tree-text: #000000;
  --toggle-button-background-color: #ffff;
  --toggle-button-icon-background-color: #ffff;
  --toggle-switch-checked-bg-color: #851585;
  --preview-page-bg-color: #ffff;
  --pill-text-color: #000;
  --code-viewer-header-tab: #858AAD;
  --icon-disabled-color: #8a8d9a; /* Darker gray for better visibility in light mode */
  --icon-enabled-color: #52577a;
  --icon-disabled-opacity: 0.4; /* Higher opacity for better visibility */

  // Prompt bar disabled state variables - Light Theme
  --prompt-bar-disabled-border: #ccc;
  --prompt-bar-disabled-bg: rgba(240, 240, 245, 0.3);
  --prompt-bar-disabled-text: #999;
  --logs-container-bg: #ffffff;
  --logs-container-border: #e0e0e0;
  --logs-header-bg: #f8f9fa;
  --logs-header-border: #e0e0e0;
  --logs-content-bg: #f5f5f5;
  --logs-text-color: #333333;
  --logs-filter-hover-bg: rgba(0, 0, 0, 0.05);
  --logs-filter-active-bg: rgba(0, 0, 0, 0.1);
  --logs-info-message-bg: rgba(2, 136, 209, 0.1);
  --logs-info-message-color: #0288d1;
  --logs-info-color: #0288d1;
  --logs-debug-color: #7b1fa2;
  --logs-warning-color: #ffa000;
  --logs-error-color: #d32f2f;
  --logs-streaming-bg: rgba(2, 136, 209, 0.1);
  --logs-streaming-color: #0288d1;

  // split screen Resizer
  --awe-split-screen-resizer: #fff;
  --awe-split-screen-resizer-hover: #808080;

  // Split Screen Header
  --awe-split-screen-header-bg-color: rgba(240, 240, 245, 0.5);
  --awe-split-border-color: #dadce7;
  --chat-window-card-bg: #f8f8ff;
  --chat-window-text-color: #14161f;
  --chat-window-icon-color: #666d99;

  /* Scrollbar Variables - Light Theme */
  --scrollbar-thumb: rgba(101, 102, 205, 0.3);
  --scrollbar-thumb-hover: rgba(101, 102, 205, 0.5);
  --chat-window-card-bg-color: #f8f8ff;
  --chat-window-card-border: transparent;
  --modal-text-color:#14161f;
  --code-viewer-content-bg:#f8f8ff;
}

.dark-theme {
  // Dark Theme Variables
  overflow: auto;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-image: url(../svgs/main-bg.svg);
  // --background-gradient-color:linear-gradient(to bottom right, #a855f7, #3b82f6, #ec4899, #f97316);
  --background-color: #121212;
  --body-text-color: #e0e0e0;
  --card-border-color: #424242;
  --card-hover-bg: #2a2a2a;
  --selected-card-border: #7986cb;
  --selected-card-bg: #303f9f;
  --button-bg: #424242;
  --button-hover-bg: #525252;
  --summary-bg: #2a2a2a;
  --summary-border: #424242;
  --hero-section-color: #ffffff;
  --hero-section-heading-color: #33364d;
  --option-card-background-color: none;
  --header-icon: #ffffff;
  --header-icon-border: #ffffff;
  --button-bg-color: #e04574;
  --button-text-color: #ededf3;
  --button-border-color: #474c6b;
  --prompt-bar-background-color: #8b8dda;
  --prompt-bar-border-color: #8b8dda;
  --prompt-bar-hover-color: #f96cab;
  --feature-card-color: #ffffff;
  --prompt-bar-suggestion-button-bg: linear-gradient(
    102deg,
    rgba(20, 27, 31, 0.36) 2.05%,
    rgba(20, 27, 31, 0.36) 100%
  );

  --main-title: #fff;
  --main-subtitle: #fff;

  // feature-card-main-page
  --feature-card-btn-text-color: #ffffff;
  --feature-card-border-front: #fff;
  --feature-card-text-color: #fff;
  --feature-card-border-back: #e30a6d;
  --feature-card-bg: linear-gradient(
    102deg,
    rgba(20, 27, 31, 0.24) 2.05%,
    rgba(20, 27, 31, 0.24) 100%
  );
  --feature-card-btn: linear-gradient(
    90deg,
    var(--Blue-B-600, rgba(66, 68, 194, 0.75)) 0%,
    var(--Pink-P-600, rgba(193, 8, 92, 0.75)) 100%
  );

  // form-factor-component dark theme
  --form-factor-btn-border: #fff;
  --form-factor-btn-bg: none;
  --form-factor-card-hover: linear-gradient(90deg, #6566cd 24%, #f63b8f 68%);
  --form-factor-btn-text-color: #fff;
  --right-panel-header-bg: #14161f;

  // Code Viewer Variables - Dark Theme
  --code-viewer-bg: rgba(20, 27, 31, 0.24);
  --code-viewer-text: #ffffff;
  --code-viewer-border: #292c3d;
  --code-viewer-file-hover: rgba(255, 255, 255, 0.1);
  --code-viewer-folder-hover: rgba(255, 255, 255, 0.1);
  --code-viewer-search-bg: transparent;
  --code-viewer-search-border: #292c3d;
  --code-viewer-search-focus: #4b4e61;
  --code-viewer-search-placeholder: #666;
  --code-viewer-arrow: #fff;
  --code-viewer-tab-bg: rgba(255, 255, 255, 0.05);
  --code-viewer-tab-hover: rgba(255, 255, 255, 0.1);
  --code-viewer-tab-text: #fff;
  --code-viewer-monaco-bg: transparent;
  --code-viewer-tree-text: #ffffff;
  --toggle-button-background-color: #1a1c25;
  --toggle-button-icon-background-color: #14161f;
  --toggle-switch-checked-bg-color: #6366f1;
  --preview-page-bg-color: #1a1c25;
  --pill-text-color: #fff;
  --loading-text-color: #ffffff;
  --code-viewer-header-tab:#292C3D
  --icon-disabled-color: #c2c5d6; /* Light gray for dark mode */
  --icon-enabled-color: #ffffff; /* Bright white for enabled state in dark mode */
  --icon-disabled-opacity: 0.4; /* Opacity for disabled state */

  // Prompt bar disabled state variables - Dark Theme
  --prompt-bar-disabled-border-dark: #555;
  --prompt-bar-disabled-bg-dark: rgba(50, 50, 50, 0.3);
  --prompt-bar-disabled-text-dark: #666;

  // Logs Container Variables - Dark Theme
  --logs-container-bg: #14161f;
  --logs-container-border: #292c3d;
  --logs-header-bg: #1a1c25;
  --logs-header-border: #292c3d;
  --logs-content-bg: #14161f;
  --logs-text-color: #ffffff;
  --logs-filter-hover-bg: rgba(255, 255, 255, 0.1);
  --logs-filter-active-bg: rgba(255, 255, 255, 0.15);
  --logs-info-message-bg: rgba(2, 136, 209, 0.2);
  --logs-info-message-color: #64b5f6;
  --logs-info-color: #64b5f6;
  --logs-debug-color: #ce93d8;
  --logs-warning-color: #ffcc80;
  --logs-error-color: #ef9a9a;
  --logs-streaming-bg: rgba(2, 136, 209, 0.2);
  --logs-streaming-color: #64b5f6;

  // Split screen resizer
  --awe-split-screen-resizer: none;
  --awe-split-screen-resizer-hover: #808080;

  // Split Screen Header

  --awe-split-screen-header-bg-color: none;
  --awe-split-border-color: #292c3d;
  --chat-window-card-bg: #33364d;
  --chat-window-text-color: #ffffff;
  --chat-window-icon-color: #ffffff;

  /* Scrollbar Variables - Dark Theme */
  --scrollbar-thumb: rgba(246, 59, 143, 0.4);
  --scrollbar-thumb-hover: rgba(246, 59, 143, 0.6);
  --chat-window-card-bg-color: linear-gradient(
    102.14deg,
    rgba(20, 27, 31, 0.24) 2.05%,
    rgba(20, 27, 31, 0.24) 100%
  );
  --chat-window-card-border: rgba(255, 255, 255, 1);
  --modal-text-color:#ffffff;
  --code-viewer-content-bg:#14161f;
}
